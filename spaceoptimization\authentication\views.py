from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from .services import AzureADService
from .models import Users, Role, UserActivityLog, ApiRequestLog, UserConceptMapping
from django.utils import timezone

class AzureCallbackView(APIView):
    """Exchanges authorization code for tokens and user info"""
    authentication_classes = []  
    permission_classes = []      

    def post(self, request):
        # Extract code and redirect_uri from POST body
        code = request.data.get("code")
        redirect_uri = request.data.get("redirect_uri")
        # print(f"Code: {code}, Redirect URI: {redirect_uri}")
        if not code or not redirect_uri:
            return Response({
                "success": False,
                "error": "Missing authorization code or redirect URI"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Initialize service and exchange code for tokens
            azure_service = AzureADService()
            tokens = azure_service.exchange_code_for_tokens(code, redirect_uri)
            access_token = tokens.get('access_token')
            id_token = tokens.get('id_token')
            refresh_token = tokens.get('refresh_token')

            if not access_token or not id_token:
                return Response({
                    "success": False,
                    "error": "Failed to obtain tokens"
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get user profile
            user_profile = azure_service.get_user_profile(access_token, id_token)
            
            # Check if user exists in the database
            # print(user_profile,'user email')
            try:
                user = Users.objects.get(email=user_profile.get('email'))
                # print(user, 'user found')
                
                # Check if user is active
                if not user.is_active:
                    return Response({
                        "success": False,
                        "error": "User account is not active"
                    }, status=status.HTTP_403_FORBIDDEN)
                
                # Update last login time
                user.updated_at = timezone.now()
                user.save()
                
                UserActivityLog.objects.create(
                    user=user,
                    role=user.role,
                    login_at=timezone.now()
                )
                # Add user details to response
                user_data = {
                    "id": user.id,
                    "email": user.email,
                    "name": user.first_name + " " + user.last_name,
                    "role": user.role.name if user.role else None,
                    "is_active": user.is_active
                }

                return Response({
                    "success": True,
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "user": user_data
                }, status=status.HTTP_200_OK)

            except Users.DoesNotExist:
                return Response({
                    "success": False,
                    "error": "User is not authorized to access this application"
                }, status=status.HTTP_401_UNAUTHORIZED)

        except Exception as e:
            return Response({
                "success": False,
                "error": f"Authorization error: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserActivityView(APIView):
    authentication_classes = []  
    permission_classes = [] 
    def get(self, request):
        try:
            logs = UserActivityLog.objects.select_related('user', 'role').all()

            data = [
                {   
                    "id": log.id,
                    "user_id": log.user.id,
                    "first_name": log.user.first_name,
                    "last_name": log.user.last_name,
                    "email": log.user.email,
                    "role": log.role.name if log.role else None,
                    "login_at": log.login_at.strftime("%Y-%m-%d %H:%M:%S UTC"),
                }
                for log in logs
            ]

            return Response(data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                "success": False,
                "error": f"Error fetching login history: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ApiRequestLogView(APIView):
    """View to retrieve API request logs"""
    authentication_classes = []  
    permission_classes = [] 
    def get(self, request):
        """Get the last 100 API request logs"""
        try:
            logs = ApiRequestLog.objects.order_by('-timestamp')[:100]
            data = [
                {
                    "timestamp": log.timestamp,
                    "endpoint": log.endpoint,
                    "user_id": log.user.id if log.user else None,
                    "email": log.user.email,
                    "first_name": log.user.first_name,
                    "last_name": log.user.last_name,
                    "status_code": log.status_code,
                } for log in logs
            ]
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "success": False,
                "error": f"Failed to retrieve logs: {str(e)}"
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class CreateUserWithConceptAPIView(APIView):
    """
    API endpoint to create a user and assign a single concept.

    Behavior:
    - If the user does not exist, a new user is created with:
        - first_name and last_name parsed from email
        - role_id = 1
        - is_active = True
        - created_at and updated_at set to current timestamp
    - If the concept already exists for the user, returns an error with HTTP 400.
    - If the concept does not exist, it is added for the user.
    - Returns all concepts currently assigned to the user.

	Request payload:
	{
	"email":"<user email>",
	"concept":"<concept name>"
	}

    Response:
    - Success (HTTP 201):
        {
            "success": True,
            "email": "<user_email>",
            "concept": ["list", "of", "concepts"]
        }
    - Error (HTTP 400):
        {
            "success": False,
            "message": "Concept '<concept>' already exists for user '<email>'"
        }
    """
    def post(self, request):
        email = request.data.get('email')
        concept = request.data.get('concept')

        # Validate inputs
        if not email or not concept:
            return Response({
                "success": False,
                "message": "Missing required parameters: email and concept"
            }, status=status.HTTP_400_BAD_REQUEST)

        concept = str(concept).strip().lower()

        local_part = email.split('@')[0]
        if '.' in local_part:
            first_name, last_name = local_part.split('.', 1)
        else:
            first_name = local_part
            last_name = ''

        # Get or create user
        user,user_created = Users.objects.get_or_create(
            email=email,
            defaults={
                "first_name": first_name,
                "last_name": last_name,
                "role_id": 1,
                "is_active": True,
                "created_at": timezone.now(),
                "updated_at": timezone.now()
            }
        )

        if UserConceptMapping.objects.filter(user_id=user.id, concept_nm=concept).exists():
            return Response({
                "success": False,
                "message": f"Concept '{concept}' already exists for user '{email}'"
            }, status=status.HTTP_400_BAD_REQUEST)
        
        concept_mapping = UserConceptMapping.objects.create(
            user_id=user.id,
            concept_nm=concept
        )

        all_concepts = list(
            UserConceptMapping.objects.filter(user_id=user.id)
            .values_list('concept_nm', flat=True)
        )
        return Response({
            "success": True,
            "email": user.email,
            "concept": all_concepts
        }, status=status.HTTP_201_CREATED)