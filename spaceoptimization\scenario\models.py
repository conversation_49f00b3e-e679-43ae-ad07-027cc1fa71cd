from django.db import models

class FileUpload(models.Model):
    id = models.AutoField(primary_key=True)   # or whatever is the PK in `file_uploads`
    file_name = models.CharField(max_length=128)
    file_type = models.CharField(max_length=32)
    uploaded_at = models.DateTimeField()
    url = models.CharField(max_length=256)

    class Meta:
        db_table = 'file_uploads'
        managed = False  

class ScenarioMetad(models.Model):
    name = models.CharField(max_length=128)
    user_id = models.IntegerField(null=True, blank=True) #remove null after users table is added
    season_type = models.CharField(max_length=32)
    eval_type = models.CharField(max_length=32,blank=True,null=True)
    event_name = models.CharField(max_length=128)
    eval_start = models.DateTimeField()
    eval_end = models.DateTimeField()
    ref_start = models.DateField(max_length=128)
    ref_end = models.DateField(max_length=128)
    CNCPT_NM = models.CharField(max_length=64)
    TERRITORY_NM = models.CharField(max_length=64)
    metric = models.CharField(max_length=64)
    sqft_file = models.ForeignKey(FileUpload, null=True, blank=True, on_delete=models.SET_NULL, related_name='sqft_files')
    mdq_file = models.ForeignKey(FileUpload, null=True, blank=True, on_delete=models.SET_NULL, related_name='mdq_files')
    cover_file = models.ForeignKey(FileUpload, null=True, blank=True, on_delete=models.SET_NULL, related_name='cover_files')
    exclusion_file_id = models.IntegerField(null=True, blank=True)
    loc_cd = models.CharField(max_length=256)
    created_by = models.IntegerField(null=True, blank=True) #remove null after users table is added
    updated_by = models.IntegerField(null=True, blank=True) #remove null after users table is added
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    weights = models.JSONField(blank=True,null=True)
    sqft_filename = models.CharField(max_length=255, null=True, blank=True)
    mdq_filename = models.CharField(max_length=255, null=True, blank=True)
    cover_filename = models.CharField(max_length=255, null=True, blank=True)
    exclusion_filename = models.CharField(max_length=255, null=True, blank=True)
    current_page = models.IntegerField(default=0)
    progress_page = models.IntegerField(default=0)
    run_optimizer = models.BooleanField(default=0)
    run_performance = models.BooleanField(default=0)
    run_range = models.BooleanField(default=0)

    class Meta:
        managed = False
        db_table = 'SCENARIO_METAD'




class ScenarioStatus(models.Model):
    scenario = models.ForeignKey(ScenarioMetad, on_delete=models.CASCADE, db_column='scenario_id')
    status = models.CharField(max_length=64, default='CREATED')

    class Meta:
        managed = False
        db_table = 'SCENARIO_STATUS'


class BaseDeClusterModel(models.Model):
    id = models.AutoField(primary_key=True, db_column='ID')
    loc_cd = models.TextField(null=True, db_column='LOC_CD')
    loc_nm = models.TextField(null=True, db_column='LOC_NM')
    rgn_nm = models.TextField(null=True, db_column='RGN_NM')
    similarity_scores = models.TextField(null=True, db_column='SIMILARITY_SCORES')
    revenue = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='REVENUE')
    units = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='UNITS')
    customers = models.BigIntegerField(null=True, db_column='CUSTOMERS')
    total_invoice = models.BigIntegerField(null=True, db_column='TOTAL_INVOICE')
    area_sqft = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='AREA_SQFT')
    cluster_num = models.IntegerField(null=True, db_column='CLUSTER_NUM')
    volume_contribution = models.TextField(null=True, db_column='VOLUME_CONTRIBUTION')
    ethnicity_contribution = models.TextField(null=True, db_column='ETHNICITY_CONTRIBUTION')
    revenue_per_sqft = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='REVENUE_PER_SQFT')
    last_update_dt_tm = models.DateTimeField(null=True, db_column='LAST_UPDATE_DT_TM')

    class Meta:
        abstract = True


# Concept: LS
class DeLsAeCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ae_cluster'

class DeLsBhCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_bh_cluster'

class DeLsEgCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_eg_cluster'

class DeLsKsCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ks_cluster'

class DeLsKwCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_kw_cluster'

class DeLsOmCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_om_cluster'

class DeLsQtCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_qt_cluster'

class DeLsLbCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_lb_cluster'

class DeLsJdCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_ls_jd_cluster'


# Concept: HB
class DeHbAeCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ae_cluster'

class DeHbBhCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_bh_cluster'

class DeHbEgCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_eg_cluster'

class DeHbKsCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ks_cluster'

class DeHbKwCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_kw_cluster'

class DeHbOmCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_om_cluster'

class DeHbQtCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_qt_cluster'

class DeHbLbCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_lb_cluster'

class DeHbJdCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_hb_jd_cluster'


# Concept: SP
class DeSpAeCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ae_cluster'

class DeSpBhCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_bh_cluster'

class DeSpEgCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_eg_cluster'

class DeSpKsCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ks_cluster'

class DeSpKwCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_kw_cluster'

class DeSpOmCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_om_cluster'

class DeSpQtCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_qt_cluster'

class DeSpLbCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_lb_cluster'

class DeSpJdCluster(BaseDeClusterModel):
    class Meta:
        managed = False
        db_table = 'de_sp_jd_cluster'


class BaseAppClusterModel(models.Model):
    scenario_id = models.IntegerField()
    territory_nm = models.CharField(max_length=64)
    loc_cd = models.CharField(max_length=32)
    loc_nm = models.CharField(max_length=128)
    stnd_trrtry_nm = models.CharField(max_length=64)
    rgn_nm = models.CharField(max_length=64)

    revenue = models.DecimalField(max_digits=32, decimal_places=1)
    units = models.DecimalField(max_digits=32, decimal_places=1)
    customers = models.BigIntegerField()
    total_invoice = models.BigIntegerField()
    area_sqft = models.DecimalField(max_digits=32, decimal_places=1)

    cluster_num = models.IntegerField()
    volume_contribution = models.JSONField()
    ethnicity_contribution = models.JSONField()
    revenue_per_sqft = models.DecimalField(max_digits=32, decimal_places=1)

    updated_at = models.DateTimeField()
    updated_by = models.IntegerField()
    new_cluster_num = models.IntegerField()
    last_update_dt_tm = models.DateTimeField()
    similarity_scores = models.JSONField(null=True, blank=True)


    class Meta:
        abstract = True


class AppLsStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_ls_str_cluster'


class AppHbStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_hb_str_cluster'


class AppSpStrCluster(BaseAppClusterModel):
    class Meta:
        managed = False
        db_table = 'app_sp_str_cluster'


class BaseDePreoptModel(models.Model):
    id = models.AutoField(primary_key=True, db_column='ID')
    loc_cd = models.TextField(null=True, db_column='LOC_CD')
    loc_nm = models.TextField(null=True, db_column='LOC_NM')
    rgn_nm = models.TextField(null=True, db_column='RGN_NM')
    grp_nm = models.TextField(null=True, db_column='GRP_NM')
    dpt_nm = models.TextField(null=True, db_column='DPT_NM')
    clss_nm = models.TextField(null=True, db_column='CLSS_NM')
    sub_clss_nm = models.TextField(null=True, db_column='SUB_CLSS_NM')
    month = models.TextField(null=True, db_column='MONTH')
    mnth_avg_soh = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='MNTH_AVG_SOH')
    mnth_avg_itm_cnt = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='MNTH_AVG_ITM_CNT')
    mnth_avg_optn_cnt = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='MNTH_AVG_OPTN_CNT')
    mnth_end_soh = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='MNTH_END_SOH')
    mnth_end_itm_cnt = models.BigIntegerField(null=True, db_column='MNTH_END_ITM_CNT')
    mnth_end_optn_cnt = models.BigIntegerField(null=True, db_column='MNTH_END_OPTN_CNT')
    net_sls_amt = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='NET_SLS_AMT')
    rtl_qty = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='RTL_QTY')
    gmv = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='GMV')
    inv_cnt = models.BigIntegerField(null=True, db_column='INV_CNT')
    cust_cnt = models.BigIntegerField(null=True, db_column='CUST_CNT')
    str_visits = models.BigIntegerField(null=True, db_column='STR_VISITS')
    str_cust_cnt = models.BigIntegerField(null=True, db_column='STR_CUST_CNT')
    cust_pen = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='CUST_PEN')
    spc = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='SPC')
    margin_perc = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='MARGIN_PERC')
    asp = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='ASP')
    sls_per_inv = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='SLS_PER_INV')
    units_per_inv = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='UNITS_PER_INV')
    ros = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='ROS')
    cover = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='COVER')
    total_lm = models.FloatField(null=True, db_column='TOTAL_LM')
    gmv_per_day = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='GMV_PER_DAY')
    gmv_per_lm = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='GMV_PER_LM')
    lm_contribution_in_store = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='LM_CONTRIBUTION_IN_STORE')
    gmv_outlier_status = models.TextField(null=True, db_column='GMV_OUTLIER_STATUS')
    gmv_suggested_total_lm = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='GMV_SUGGESTED_TOTAL_LM')
    last_update_dt_tm = models.DateTimeField(null=True, db_column='LAST_UPDATE_DT_TM')
    gmroi = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='GMROI')
    rev_per_day = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='REV_PER_DAY')
    rev_outlier_status = models.TextField(null=True, db_column='REV_OUTLIER_STATUS')
    rev_suggested_total_lm = models.DecimalField(max_digits=32, decimal_places=2, null=True, db_column='REV_SUGGESTED_TOTAL_LM')
    trrtry_shrt_nm = models.TextField(null=True, db_column='TRRTRY_SHRT_NM')
    stnd_trrtry_nm = models.TextField(null=True, db_column='STND_TRRTRY_NM')
    cover_in_wk = models.IntegerField(null=True, db_column='COVER_IN_WK')
    area_in_sqft = models.IntegerField(null=True, db_column='AREA_IN_SQFT')

    class Meta:
        abstract = True
# Concept: LS
class DeLsAePreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ae_preopt'

class DeLsBhPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_bh_preopt'

class DeLsEgPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_eg_preopt'

class DeLsKsPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_ks_preopt'

class DeLsKwPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_kw_preopt'

class DeLsOmPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_om_preopt'

class DeLsQtPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_qt_preopt'

class DeLsLbPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_lb_preopt'

class DeLsJdPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_ls_jd_preopt'


# Concept: HB
class DeHbAePreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ae_preopt'

class DeHbBhPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_bh_preopt'

class DeHbEgPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_eg_preopt'

class DeHbKsPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_ks_preopt'

class DeHbKwPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_kw_preopt'

class DeHbOmPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_om_preopt'

class DeHbQtPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_qt_preopt'

class DeHbLbPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_lb_preopt'

class DeHbJdPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_hb_jd_preopt'


# Concept: SP
class DeSpAePreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ae_preopt'

class DeSpBhPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_bh_preopt'

class DeSpEgPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_eg_preopt'

class DeSpKsPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_ks_preopt'

class DeSpKwPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_kw_preopt'

class DeSpOmPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_om_preopt'

class DeSpQtPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_qt_preopt'

class DeSpLbPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_lb_preopt'

class DeSpJdPreopt(BaseDePreoptModel):
    class Meta:
        managed = False
        db_table = 'de_sp_jd_preopt'

class BaseAppPreoptModel(models.Model):
    id = models.BigAutoField(primary_key=True)
    scenario_id = models.IntegerField(db_column='SCENARIO_ID')
    grp_nm = models.TextField(db_column='GRP_NM')
    dpt_nm = models.TextField(db_column='DPT_NM')
    clss_nm = models.TextField(db_column='CLSS_NM')
    sub_clss_nm = models.TextField(db_column='SUB_CLSS_NM')
    loc_cd = models.TextField(db_column='LOC_CD')
    month = models.TextField(db_column='MONTH')
    total_lm = models.FloatField(db_column='TOTAL_LM')
    outlier_status_final = models.CharField(max_length=20, db_column='outlier_status_final')
    gmv_outlier_status = models.TextField(db_column='GMV_OUTLIER_STATUS', null=True, blank=True)
    gmv_suggested_total_lm = models.DecimalField(max_digits=32, decimal_places=2, db_column='GMV_SUGGESTED_TOTAL_LM', null=True, blank=True)
    rev_outlier_status = models.TextField(db_column='REV_OUTLIER_STATUS', null=True, blank=True)
    rev_suggested_total_lm = models.DecimalField(max_digits=32, decimal_places=2, db_column='REV_SUGGESTED_TOTAL_LM', null=True, blank=True)

    class Meta:
        abstract = True

class AppLsPreopt(BaseAppPreoptModel):
    class Meta:
        managed = False
        db_table = 'app_ls_preopt'


class AppHbPreopt(BaseAppPreoptModel):
    class Meta:
        managed = False
        db_table = 'app_hb_preopt'


class AppSpPreopt(BaseAppPreoptModel):
    class Meta:
        managed = False
        db_table = 'app_sp_preopt'


class FileUploads(models.Model):
    file_name = models.CharField(max_length=128, null=True, blank=True)
    file_type = models.CharField(max_length=32, null=True, blank=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)
    url = models.CharField(max_length=256, null=True, blank=True)

    def __str__(self):
        return self.file_name or f"FileUploads {self.id}"
    class Meta:
        managed = False
        db_table = 'file_uploads'

class BaseScenarioFileData (models.Model):
    scenario = models.ForeignKey("ScenarioMetad", on_delete=models.CASCADE, null=False, blank=False)
    file_upload = models.ForeignKey("FileUploads", on_delete=models.CASCADE)
    
    territory_nm = models.CharField(max_length=100, null=True, blank=True)
    loc_cd = models.CharField(max_length=100, null=True, blank=True)
    grp_nm = models.CharField(max_length=100, null=True, blank=True)
    dpt_nm = models.CharField(max_length=100, null=True, blank=True)
    clss_nm = models.CharField(max_length=100, null=True, blank=True)
    sub_clss_nm = models.CharField(max_length=100, null=True, blank=True)
    STND_TRRTRY_NM = models.CharField(max_length=100, null=True, blank=True)
    uploaded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        abstract = True

class SqftFileData(BaseScenarioFileData ):
    sqft = models.FloatField(null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'sqft_filedata'

class MdqFileData(BaseScenarioFileData ):
    mdq = models.FloatField(null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'mdq_filedata'


class CoverFileData(BaseScenarioFileData ):
    mid_cover_start = models.FloatField(null=True, blank=True)
    high_cover_start = models.FloatField(null=True, blank=True)
    depth = models.FloatField(null=True, blank=True)
    class Meta:
        managed = False
        db_table = 'cover_filedata'

class ExclusionFileData(BaseScenarioFileData ):
    class Meta:
        managed = False
        db_table = 'exclusion_filedata'


class AdjustedDataHB(models.Model):
    grp_nm = models.CharField(max_length=100, db_column='GRP_NM')
    dpt_nm = models.CharField(max_length=100, db_column='DPT_NM')
    clss_nm = models.CharField(max_length=100, db_column='CLSS_NM')
    sub_clss_nm = models.CharField(max_length=100, db_column='SUB_CLSS_NM')
    loc_cd = models.CharField(max_length=50, db_column='LOC_CD')
    month = models.CharField(max_length=10, db_column='MONTH')

    total_lm = models.FloatField(db_column='TOTAL_LM')
    min_lm = models.FloatField(db_column='MIN_LM')
    current_cover_in_days = models.FloatField(db_column='current_cover_in_days')
    gmv_per_lm = models.FloatField(db_column='GMV_PER_LM')
    gmv = models.FloatField(db_column='GMV')
    net_sls_amt = models.FloatField(db_column='NET_SLS_AMT')
    depth = models.FloatField(db_column='DEPTH')
    performance = models.FloatField(db_column='Performance')

    gmv_sum_reference_month = models.FloatField(db_column='GMV_sum_reference_month')
    net_sls_amt_sum_reference_month = models.FloatField(db_column='NET_SLS_AMT_sum_reference_month')
    total_lm_avg = models.FloatField(db_column='TOTAL_LM_AVG')
    avg_optn_cnt = models.FloatField(db_column='AVG_OPTN_CNT')
    option_density = models.FloatField(db_column='OPTION_DENSITY')

    current_lm = models.FloatField(db_column='current_lm')
    max_sat_lm = models.FloatField(db_column='max_sat_lm')

    gmv_per_linear_meter_ref_months = models.FloatField(db_column='GMV_per_linear_meter_ref_months')
    net_sls_amt_per_linear_meter_ref_months = models.FloatField(db_column='NET_SLS_AMT_per_linear_meter_ref_months')
    cover_penalty = models.FloatField(db_column='cover_penalty')
    adjusted_performance = models.FloatField(db_column='adjusted_performance')

    perf_bucket = models.CharField(max_length=50, db_column='perf_bucket')
    lm_bucket = models.CharField(max_length=50, db_column='lm_bucket')
    cover_bucket = models.CharField(max_length=50, db_column='cover_bucket')
    action = models.CharField(max_length=50, db_column='action')

    lb = models.FloatField(db_column='lb')
    ub = models.FloatField(db_column='ub')
    status = models.CharField(max_length=50, db_column='status')

    optimized_lm = models.FloatField(db_column='optimized_lm')
    lm_delta = models.FloatField(db_column='lm_delta')
    change = models.CharField(max_length=50, db_column='change')
    space_change_percent = models.FloatField(db_column='space_change_percent')

    optimized_lm_before_devi_adjust = models.FloatField(db_column='optimized_lm_before_devi_adjust')
    adjustment = models.TextField(db_column='adjustment')
    final_action = models.TextField(db_column='Final_Action')
    new_metric = models.FloatField(db_column='new_metric')
    optimized_no_of_options = models.FloatField(db_column='optimized_no_of_options')
    optimized_qty = models.FloatField(db_column='optimized_qty')

    days_btw_reference_period = models.BigIntegerField(db_column='days_btw_reference_period')
    current_per_day_metric = models.FloatField(db_column='current_per_day_metric')
    current_per_day_metric_per_lm = models.FloatField(db_column='current_per_day_metric_per_lm')
    new_metric_per_day = models.FloatField(db_column='new_metric_per_day')
    pipeline_version = models.TextField(db_column='pipeline_version')
    run_timestamp = models.TextField(db_column='run_timestamp')  # Change to DateTimeField if it's an actual datetime
    config_group_filter = models.TextField(db_column='config_group_filter')
    config_optimization_on = models.TextField(db_column='config_optimization_on')
    created_at = models.DateTimeField(db_column='created_at')
    scenario_id = models.IntegerField()

    class Meta:
        managed = False
        db_table = 'adjusted_data_hb'


class OverallPerformanceDataHb(models.Model):

    territory_nm = models.TextField()
    loc_cd = models.TextField()
    loc_nm = models.TextField()
    stnd_trrtry_nm = models.TextField()
    rgn_nm = models.TextField()
    grp_nm = models.TextField()
    dpt_nm = models.TextField()
    clss_nm = models.TextField()
    sub_clss_nm = models.TextField()

    month = models.BigIntegerField()

    mnth_avg_soh = models.FloatField()
    mnth_avg_itm_cnt = models.FloatField()
    mnth_avg_optn_cnt = models.FloatField()
    mnth_end_soh = models.FloatField()
    mnth_end_itm_cnt = models.BigIntegerField()
    mnth_end_optn_cnt = models.BigIntegerField()
    net_sls_amt = models.FloatField()
    rtl_qty = models.FloatField()
    gmv = models.FloatField()
    inv_cnt = models.BigIntegerField()
    cust_cnt = models.BigIntegerField()
    str_visits = models.BigIntegerField()
    str_cust_cnt = models.BigIntegerField()
    cust_pen = models.FloatField()
    spc = models.FloatField()
    margin_perc = models.FloatField()
    asp = models.FloatField()
    sls_per_inv = models.FloatField()
    units_per_inv = models.FloatField()
    ros = models.TextField()
    cover = models.FloatField()
    total_lm = models.FloatField()
    gmv_per_day = models.FloatField()
    gmv_per_lm = models.FloatField()
    lm_contribution_in_store = models.FloatField()
    outlier_status = models.TextField()
    suggested_total_lm = models.FloatField()

    last_update_dt_tm = models.DateTimeField()
    outlier_status_final = models.TextField()
    min_lm = models.FloatField()
    d_sales = models.FloatField()
    d_units = models.FloatField()
    d_margin_abs = models.FloatField()
    nd_gmv_per_lm = models.FloatField()
    nd_d_sales = models.FloatField()
    nd_d_margin_abs = models.FloatField()
    nd_spc = models.FloatField()
    nd_asp = models.FloatField()
    nd_margin_perc = models.FloatField()
    nd_sls_per_inv = models.FloatField()
    nd_cover = models.BigIntegerField()
    log_space = models.FloatField()
    performance = models.FloatField()
    cluster_num = models.BigIntegerField()

    pipeline_version = models.TextField()
    run_timestamp = models.TextField()
    config_group_filter = models.TextField()
    config_optimization_on = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        managed = False
        db_table = "overall_performance_data_hb"


class BaseOptimizationSummary(models.Model):
    id = models.BigAutoField(primary_key=True)  # Auto increment PK
    STND_TRRTRY_NM = models.TextField(null=True, blank=True)
    GRP_NM = models.TextField(null=True, blank=True)
    DPT_NM = models.TextField(null=True, blank=True)
    CLSS_NM = models.TextField(null=True, blank=True)
    SUB_CLSS_NM = models.TextField(null=True, blank=True)
    LOC_CD = models.TextField(null=True, blank=True)
    TOTAL_LM = models.FloatField(null=True, blank=True)
    COVER = models.FloatField(null=True, blank=True)
    GMV_PER_LM = models.FloatField(null=True, blank=True)
    GMV = models.FloatField(null=True, blank=True)
    NET_SLS_AMT = models.FloatField(null=True, blank=True)
    DEPTH = models.FloatField(null=True, blank=True)  # renamed to match DB
    CLUSTER_NUM = models.BigIntegerField(null=True, blank=True)
    ROS = models.FloatField(null=True, blank=True)
    Performance = models.FloatField(null=True, blank=True)
    GMV_sum_reference_month = models.FloatField(null=True, blank=True)
    NET_SLS_AMT_sum_reference_month = models.FloatField(null=True, blank=True)
    TOTAL_LM_AVG = models.FloatField(null=True, blank=True)
    AVG_OPTN_CNT = models.FloatField(null=True, blank=True)
    MNTH_AVG_SOH_AVG = models.FloatField(null=True, blank=True)
    MIN_LM = models.FloatField(null=True, blank=True)
    OPTION_DENSITY = models.FloatField(null=True, blank=True)
    current_lm = models.FloatField(null=True, blank=True)
    max_sat_lm = models.FloatField(null=True, blank=True)
    GMV_per_linear_meter_ref_months = models.FloatField(null=True, blank=True)
    NET_SLS_AMT_per_linear_meter_ref_months = models.FloatField(null=True, blank=True)
    cover_penalty = models.FloatField(null=True, blank=True)
    adjusted_performance = models.FloatField(null=True, blank=True)
    perf_bucket = models.TextField(null=True, blank=True)
    lm_bucket = models.TextField(null=True, blank=True)
    cover_bucket = models.TextField(null=True, blank=True)
    action = models.TextField(null=True, blank=True)
    lb = models.FloatField(null=True, blank=True)
    ub = models.FloatField(null=True, blank=True)
    status = models.TextField(null=True, blank=True)
    optimized_lm = models.FloatField(null=True, blank=True)
    lm_delta = models.FloatField(null=True, blank=True)
    change = models.TextField(null=True, blank=True)
    space_change_precent = models.FloatField(null=True, blank=True)
    optimized_lm_before_devi_adjust = models.FloatField(null=True, blank=True)
    adjustment = models.TextField(null=True, blank=True)
    Final_Action = models.TextField(null=True, blank=True)
    new_metric = models.FloatField(null=True, blank=True)
    optimized_no_of_options = models.FloatField(null=True, blank=True)
    optimized_qty = models.FloatField(null=True, blank=True)
    lm_per_option = models.FloatField(null=True, blank=True)
    days_btw_reference_period = models.BigIntegerField(null=True, blank=True)
    current_per_day_metric = models.FloatField(null=True, blank=True)
    current_per_day_metric_per_lm = models.FloatField(null=True, blank=True)
    new_metric_per_day = models.FloatField(null=True, blank=True)
    scenario_id = models.BigIntegerField(null=True, blank=True)
    evaluation_period_new_matric = models.FloatField(null=True, blank=True)

    class Meta:
        abstract = True  # base, won’t create a table


# HB Table
class HbOptimizationSummary(BaseOptimizationSummary):
    class Meta:
        managed = False
        db_table = 'hb_optimization_summary'


# LS Table
class LsOptimizationSummary(BaseOptimizationSummary):
    class Meta:
        managed = False
        db_table = 'ls_optimization_summary'


# SP Table
class SpOptimizationSummary(BaseOptimizationSummary):
    class Meta:
        managed = False
        db_table = 'sp_optimization_summary'

class BaseSatPlot(models.Model):
    id = models.BigAutoField(primary_key=True)
    STND_TRRTRY_NM = models.TextField(null=True, blank=True)
    CLUSTER_NUM = models.BigIntegerField(null=True, blank=True)
    GRP_NM = models.TextField(null=True, blank=True)
    DPT_NM = models.TextField(null=True, blank=True)
    CLSS_NM = models.TextField(null=True, blank=True)
    SUB_CLSS_NM = models.TextField(null=True, blank=True)
    x_raw = models.TextField(null=True, blank=True)
    y = models.TextField(null=True, blank=True)
    x_gauss = models.TextField(null=True, blank=True)
    x_pred_gauss = models.TextField(null=True, blank=True)
    y_pred_gauss = models.TextField(null=True, blank=True)
    x_pred_lm = models.TextField(null=True, blank=True)
    y_pred = models.TextField(null=True, blank=True)
    density = models.TextField(null=True, blank=True)
    density_sorted = models.TextField(null=True, blank=True)
    refined_sat_gauss = models.FloatField(null=True, blank=True)
    sat_lm = models.FloatField(null=True, blank=True)
    perf_at_sat = models.FloatField(null=True, blank=True)
    PolynomialDegree = models.BigIntegerField(null=True, blank=True)
    Coefficients = models.TextField(null=True, blank=True)
    Normal_R2 = models.FloatField(null=True, blank=True)
    Ridge_R2 = models.FloatField(null=True, blank=True)
    scenario_id = models.BigIntegerField(null=True, blank=True)

    class Meta:
        abstract = True


class DsHbSatPlot(BaseSatPlot):
    class Meta:
        db_table = "ds_hb_sat_plot"


class DsLsSatPlot(BaseSatPlot):
    class Meta:
        db_table = "ds_ls_sat_plot"


class DsSpSatPlot(BaseSatPlot):
    class Meta:
        db_table = "ds_sp_sat_plot"

from django.db import models


class BasePerformanceRef(models.Model):
    id = models.BigAutoField(primary_key=True)
    TERRITORY_NM = models.TextField(null=True, blank=True)
    LOC_CD = models.TextField(null=True, blank=True)
    LOC_NM = models.TextField(null=True, blank=True)
    STND_TRRTRY_NM = models.TextField(null=True, blank=True)
    RGN_NM = models.TextField(null=True, blank=True)
    GRP_NM = models.TextField(null=True, blank=True)
    DPT_NM = models.TextField(null=True, blank=True)
    CLSS_NM = models.TextField(null=True, blank=True)
    SUB_CLSS_NM = models.TextField(null=True, blank=True)
    MONTH = models.BigIntegerField(null=True, blank=True)
    MNTH_AVG_SOH = models.FloatField(null=True, blank=True)
    MNTH_AVG_ITM_CNT = models.FloatField(null=True, blank=True)
    MNTH_AVG_OPTN_CNT = models.FloatField(null=True, blank=True)
    MNTH_END_SOH = models.FloatField(null=True, blank=True)
    MNTH_END_ITM_CNT = models.BigIntegerField(null=True, blank=True)
    MNTH_END_OPTN_CNT = models.BigIntegerField(null=True, blank=True)
    NET_SLS_AMT = models.FloatField(null=True, blank=True)
    RTL_QTY = models.FloatField(null=True, blank=True)
    GMV = models.FloatField(null=True, blank=True)
    INV_CNT = models.BigIntegerField(null=True, blank=True)
    CUST_CNT = models.BigIntegerField(null=True, blank=True)
    STR_VISITS = models.BigIntegerField(null=True, blank=True)
    STR_CUST_CNT = models.BigIntegerField(null=True, blank=True)
    CUST_PEN = models.FloatField(null=True, blank=True)
    SPC = models.FloatField(null=True, blank=True)
    MARGIN_PERC = models.FloatField(null=True, blank=True)
    ASP = models.FloatField(null=True, blank=True)
    SLS_PER_INV = models.FloatField(null=True, blank=True)
    UNITS_PER_INV = models.FloatField(null=True, blank=True)
    ROS = models.BigIntegerField(null=True, blank=True)
    TOTAL_LM = models.FloatField(null=True, blank=True)
    GMV_PER_DAY = models.FloatField(null=True, blank=True)
    GMV_PER_LM = models.FloatField(null=True, blank=True)
    LM_CONTRIBUTION_IN_STORE = models.FloatField(null=True, blank=True)
    OUTLIER_STATUS = models.TextField(null=True, blank=True)
    SUGGESTED_TOTAL_LM = models.FloatField(null=True, blank=True)
    LAST_UPDATE_DT_TM = models.DateTimeField(null=True, blank=True)
    outlier_status_final = models.TextField(null=True, blank=True)
    scenario_id = models.IntegerField(null=True, blank=True)
    MIN_LM = models.FloatField(null=True, blank=True)
    COVER = models.IntegerField(null=True, blank=True)
    D_SALES = models.FloatField(null=True, blank=True)
    D_UNITS = models.FloatField(null=True, blank=True)
    D_MARGIN_ABS = models.FloatField(null=True, blank=True)
    ND_GMV_PER_LM = models.FloatField(null=True, blank=True)
    ND_D_SALES = models.FloatField(null=True, blank=True)
    ND_D_MARGIN_ABS = models.FloatField(null=True, blank=True)
    ND_SPC = models.FloatField(null=True, blank=True)
    ND_ASP = models.FloatField(null=True, blank=True)
    ND_MARGIN_PERC = models.FloatField(null=True, blank=True)
    ND_SLS_PER_INV = models.FloatField(null=True, blank=True)
    ND_COVER = models.FloatField(null=True, blank=True)
    LOG_SPACE = models.FloatField(null=True, blank=True)
    Performance = models.FloatField(null=True, blank=True)
    CLUSTER_NUM = models.BigIntegerField(null=True, blank=True)
    pipeline_version = models.TextField(null=True, blank=True)
    run_timestamp = models.TextField(null=True, blank=True)
    config_group_filter = models.TextField(null=True, blank=True)
    config_optimization_on = models.TextField(null=True, blank=True)
    lm_bucket_p = models.TextField(null=True, blank=True)
    cover_bucket_p = models.TextField(null=True, blank=True)
    action_p = models.TextField(null=True, blank=True)
    perf_bucket_p = models.TextField(null=True, blank=True)
    Linear_meter = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    Productivity = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    cover_penalty = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    adjusted_performance = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    new_action = models.CharField(max_length=20, null=True, blank=True)

    class Meta:
        abstract = True


class DeHbPerformanceRef(BasePerformanceRef):
    class Meta:
        db_table = "ds_hb_performance_ref"


class DeLsPerformanceRef(BasePerformanceRef):
    class Meta:
        db_table = "ds_ls_performance_ref"


class DeSpPerformanceRef(BasePerformanceRef):
    class Meta:
        db_table = "ds_sp_performance_ref"


class BaseEvaluationMetrics(models.Model):
    loc_cd = models.TextField(null=True, blank=True)
    metric_type = models.TextField(null=True, blank=True)
    current_total = models.FloatField(null=True, blank=True)
    new_total = models.FloatField(null=True, blank=True)
    lift = models.FloatField(null=True, blank=True)
    pct_lift = models.FloatField(null=True, blank=True)
    current_daily = models.FloatField(null=True, blank=True)
    new_daily = models.FloatField(null=True, blank=True)
    daily_lift = models.FloatField(null=True, blank=True)
    daily_pct_lift = models.FloatField(null=True, blank=True)
    scenario_id = models.BigIntegerField(null=True, blank=True)
    evaluation_days = models.IntegerField(null=True, blank=True)
    evaluation_period_new_matric = models.FloatField(null=True, blank=True)

    class Meta:
        abstract = True  # ✅ ensures no table is created for this base class


class HbEvaluationMetrics(BaseEvaluationMetrics):
    class Meta:
        db_table = "hb_evaluation_metrics"


class LsEvaluationMetrics(BaseEvaluationMetrics):
    class Meta:
        db_table = "ls_evaluation_metrics"


class SpEvaluationMetrics(BaseEvaluationMetrics):
    class Meta:
        db_table = "sp_evaluation_metrics"

class BaseRangeEvaluationMetrics(models.Model):
    loc_cd = models.CharField(max_length=50, null=True, blank=True, db_column='loc_cd')
    metric_type = models.CharField(max_length=100, null=True, blank=True, db_column='metric_type')
    current_total = models.DecimalField(max_digits=18, decimal_places=2, null=True, blank=True, db_column='current_total')
    new_total = models.DecimalField(max_digits=18, decimal_places=2, null=True, blank=True, db_column='new_total')
    lift = models.DecimalField(max_digits=18, decimal_places=2, null=True, blank=True, db_column='lift')
    pct_lift = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, db_column='pct_lift')
    current_daily = models.DecimalField(max_digits=18, decimal_places=2, null=True, blank=True, db_column='current_daily')
    new_daily = models.DecimalField(max_digits=18, decimal_places=2, null=True, blank=True, db_column='new_daily')
    daily_lift = models.DecimalField(max_digits=18, decimal_places=2, null=True, blank=True, db_column='daily_lift')
    daily_pct_lift = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, db_column='daily_pct_lift')
    scenario_id = models.BigIntegerField(null=True, blank=True, db_column='scenario_id')
    evaluation_days = models.IntegerField(null=True, blank=True, db_column='evaluation_days')
    evaluation_period_new_matric = models.DecimalField(max_digits=18, decimal_places=2, null=True, blank=True, db_column='evaluation_period_new_matric')

    class Meta:
        abstract = True


class HbRangeEvaluationMetrics(BaseRangeEvaluationMetrics):
    class Meta:
        managed = False
        db_table = 'hb_range_evaluation_metrics'


class LsRangeEvaluationMetrics(BaseRangeEvaluationMetrics):
    class Meta:
        managed = False
        db_table = 'ls_range_evaluation_metrics'


class SpRangeEvaluationMetrics(BaseRangeEvaluationMetrics):
    class Meta:
        managed = False
        db_table = 'sp_range_evaluation_metrics'
class Territory(models.Model):
    trty_short_name = models.CharField(max_length=255, null=True, blank=True)
    trty_display_name = models.CharField(max_length=255, null=True, blank=True)
    concept = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = "territory"
        indexes = [
            models.Index(fields=["trty_short_name", "trty_display_name"], name="TRTY_SHORT_NAME"),
        ]

    def __str__(self):
        return f"{self.trty_display_name} ({self.trty_short_name})"


class BasePreRangeOptimizer(models.Model):
    stnd_trrtry_nm = models.TextField(null=True, blank=True)
    grp_nm = models.TextField(null=True, blank=True)
    dpt_nm = models.TextField(null=True, blank=True)
    clss_nm = models.TextField(null=True, blank=True)
    sub_clss_nm = models.TextField(null=True, blank=True)

    loc_cd = models.BigIntegerField(null=True, blank=True)
    # month = models.BigIntegerField(null=True, blank=True)

    total_lm = models.FloatField(null=True, blank=True)
    min_lm = models.FloatField(null=True, blank=True)
    cover = models.BigIntegerField(null=True, blank=True)

    gmv_per_lm = models.FloatField(null=True, blank=True)
    gmv = models.FloatField(null=True, blank=True)
    net_sls_amt = models.FloatField(null=True, blank=True)
    depth = models.TextField(null=True, blank=True)  # Changed from FloatField to TextField
    cluster_num = models.TextField(null=True, blank=True)  # New field

    performance = models.FloatField(null=True, blank=True)
    gmv_sum_reference_month = models.FloatField(null=True, blank=True)
    net_sls_amt_sum_reference_month = models.FloatField(null=True, blank=True)

    total_lm_avg = models.FloatField(null=True, blank=True)
    avg_optn_cnt = models.FloatField(null=True, blank=True)
    option_density = models.FloatField(null=True, blank=True)

    current_lm = models.FloatField(null=True, blank=True)
    max_sat_lm = models.FloatField(null=True, blank=True)

    gmv_per_linear_meter_ref_months = models.FloatField(null=True, blank=True)
    net_sls_amt_per_linear_meter_ref_months = models.FloatField(null=True, blank=True)

    cover_penalty = models.FloatField(null=True, blank=True)
    adjusted_performance = models.FloatField(null=True, blank=True)

    perf_bucket = models.TextField(null=True, blank=True)
    lm_bucket = models.TextField(null=True, blank=True)
    cover_bucket = models.TextField(null=True, blank=True)

    action = models.TextField(null=True, blank=True)
    lb = models.FloatField(null=True, blank=True)
    ub = models.FloatField(null=True, blank=True)
    status = models.TextField(null=True, blank=True)

    optimized_lm = models.FloatField(null=True, blank=True)
    lm_delta = models.FloatField(null=True, blank=True)
    change = models.TextField(null=True, blank=True)
    space_change_precent = models.FloatField(null=True, blank=True)

    optimized_lm_before_devi_adjust = models.FloatField(null=True, blank=True)
    adjustment = models.TextField(null=True, blank=True)
    final_action = models.TextField(null=True, blank=True)

    new_metric = models.FloatField(null=True, blank=True)
    optimized_no_of_options = models.FloatField(null=True, blank=True)
    optimized_qty = models.TextField(null=True, blank=True)  # changed from FloatField
    lm_per_option = models.TextField(null=True, blank=True)  # changed from FloatField

    days_btw_reference_period = models.BigIntegerField(null=True, blank=True)
    current_per_day_metric = models.FloatField(null=True, blank=True)
    current_per_day_metric_per_lm = models.FloatField(null=True, blank=True)
    new_metric_per_day = models.FloatField(null=True, blank=True)

    scenario_id = models.BigIntegerField(null=True, blank=True)
    range_based_max_lm = models.FloatField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)

    class Meta:
        abstract = True


class HbAePreRangeOptimizer(BasePreRangeOptimizer):
    class Meta:
        managed = False
        db_table = 'hb_ae_pre_range_optimizer'

class HbKsPreRangeOptimizer(BasePreRangeOptimizer):
    class Meta:
        managed = False
        db_table = 'hb_ks_pre_range_optimizer'
