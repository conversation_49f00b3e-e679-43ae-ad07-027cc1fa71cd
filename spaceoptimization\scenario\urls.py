# scenario/urls.py
from django.urls import path
from . import views
urlpatterns = [
   path('createOptimizer/', views.CreateOptimizerView.as_view(), name='create-optimizer'),
   path('updateScenarioStatus/', views.UpdateScenarioStatusView.as_view(), name='update-scenario-status'),
   path('create-scenario/', views.ScenarioCreateAPIView.as_view(), name='create-scenario'),
   path('clusters/', views.ClusterByLocationView.as_view(), name='get-cluster-data'),
   path('preopt/', views.PreOptByLocation.as_view(), name='push-Preopt-data'),
   path('scenario_list/', views.ScenarioList.as_view(), name='ScenarioList'),
   path('select_store_dropdown/', views.StoreSelectionDropdown.as_view(), name='select_store_dropdown'),
   path('outlier/', views.OutlierAPIView.as_view(), name='outlier-api'),
   path('testAndControlStore/', views.testAndControlStore.as_view(), name='testAndControlStore'),
   path('runOutliers/', views.RunOutliers.as_view(), name='runOtliers'),
   path('insertTestControlStr/', views.insertTestControlStr.as_view(), name='insertTestControlStr'),
   path('getDataSummary/', views.getDataSummary.as_view(), name='getDataSummary'),
   path('getMetricGraph/', views.getMetricGraph.as_view(), name='getMetricGraph'),
   path('getAllGDCSdata/', views.getAllGDCSdata.as_view(), name='getAllGDCSdata'),
   path('health-metrics/', views.HealthMetricsAPIView.as_view(), name='health-metrics'),
   path('upload/', views.FileUploadAPIView.as_view(), name='upload'),
   path('getOptimizerDetails/', views.getOptimizerDetails.as_view(), name='getOptimizerDetails'),
   path('update_performance_action/', views.UpdatePerformanceActionView.as_view(), name='updatePerformanceAction'),
   path('update_weights/', views.UpdateScenarioWeightsView.as_view(), name='update-scenario-weights'),
   path('fetch_filtered_performance_data/', views.FilteredPerformanceDataView.as_view(), name='filtered-performance-data'),
   path('getOptimizationSummary/', views.getOptimizationSummary.as_view(), name='getOptimizationSummary'),
   path('getSaturationCurve/', views.GetSaturationCurve.as_view(), name='getSaturationCurve'),
   path('getClusterSubclassData/', views.getClusterSubclassData.as_view(), name='getClusterSubclassData'),
   path('getRefPeriod/', views.getRefPeriodData.as_view(), name='getClusterSubclassData'),
   path('getUpliftData/', views.getUpliftData.as_view(), name='getUpliftData'),
   path('completeOptimization/', views.completeOptimization.as_view(), name='completeOptimization'),
   path('downloadOutlierData/', views.DownloadOutlierData.as_view(), name='downloadOutlier'),
   path('downloadOptimizationSummary/', views.DownloadOptimizationSummary.as_view(), name='downloadOptimization'),
   path('getOptimizationFilters/', views.GetOptimizationFilters.as_view(), name='getOptimizationFilters'),
   path('getOutlierVisualization/', views.GetOutlierVisualizationAPIView.as_view(), name='getOutlierVisualization'),
   path('getTerritoryData/', views.GetTerritoryData.as_view(), name='getTerritoryData'),
   path('updateRunStatus/', views.UpdateRunStatusView.as_view(), name='updateRunStatus'),
   path('storeDataAvailability/', views.StoreDataAvailabilityAPIView.as_view(), name='distinctMonthsPerStore'),
   path('getFilterData/', views.DynamicFilterView.as_view(), name='distinctMonthsPerStore'),
   path('downloadPerformanceData/', views.DownloadPerformanceData.as_view(), name='downloadPerformanceData'),
   path('getRangeSummary/', views.getRangeSummary.as_view(), name='getRangeSummary'),
   path('downloadRangeData/', views.DownloadRangeDataCSV.as_view(), name='downloadRangeDataCSV'),
   path('downloadRangeSummary/', views.DownloadRangeSummaryCSV.as_view(), name='downloadRangeSummaryCSV'),
   path('deleteFile/', views.DeleteFileAPIView.as_view(), name='deleteFile'),
   path('getRangeUpliftData/', views.getRangeUpliftData.as_view(), name='getRangeUpliftData'),
]
