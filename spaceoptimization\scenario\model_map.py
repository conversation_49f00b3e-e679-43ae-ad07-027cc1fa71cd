from .models import *

CLUSTER_MODEL_MAP = {
    # HB
    "de_hb_ae_cluster": DeHbAeCluster,
    "de_hb_bh_cluster": DeHbBhCluster,
    "de_hb_eg_cluster": DeHbEgCluster,
    "de_hb_ks_cluster": DeHbKsCluster,
    "de_hb_kw_cluster": DeHbKwCluster,
    "de_hb_om_cluster": DeHbOmCluster,
    "de_hb_qt_cluster": DeHbQtCluster,
    "de_hb_lb_cluster": DeHbLbCluster,
    "de_hb_jd_cluster": DeHbJdCluster,

    # LS
    "de_ls_ae_cluster": DeLsAeCluster,
    "de_ls_bh_cluster": DeLsBhCluster,
    "de_ls_eg_cluster": DeLsEgCluster,
    "de_ls_ks_cluster": DeLsKsCluster,
    "de_ls_kw_cluster": DeLsKwCluster,
    "de_ls_om_cluster": DeLsOmCluster,
    "de_ls_qt_cluster": DeLsQtCluster,
    "de_ls_lb_cluster": DeLsLbCluster,
    "de_ls_jd_cluster": DeLsJdCluster,

    # SP
    "de_sp_ae_cluster": DeSpAeCluster,
    "de_sp_bh_cluster": DeSpBhCluster,
    "de_sp_eg_cluster": DeSpEgCluster,
    "de_sp_ks_cluster": DeSpKsCluster,
    "de_sp_kw_cluster": DeSpKwCluster,
    "de_sp_om_cluster": DeSpOmCluster,
    "de_sp_qt_cluster": DeSpQtCluster,
    "de_sp_lb_cluster": DeSpLbCluster,
    "de_sp_jd_cluster": DeSpJdCluster,
}

STR_CLUSTER_MODEL_MAP = {
    "app_hb_str_cluster": AppHbStrCluster,
    "app_ls_str_cluster": AppLsStrCluster,
    "app_sp_str_cluster": AppSpStrCluster
}

DE_PREOPTIMIZATION_MODEL_MAP = {
    "de_hb_ae_preopt": DeHbAePreopt,
    "de_hb_bh_preopt": DeHbBhPreopt,
    "de_hb_eg_preopt": DeHbEgPreopt,
    "de_hb_ks_preopt": DeHbKsPreopt,
    "de_hb_kw_preopt": DeHbKwPreopt,
    "de_hb_om_preopt": DeHbOmPreopt,
    "de_hb_qt_preopt": DeHbQtPreopt,
    "de_hb_lb_preopt": DeHbLbPreopt,
    "de_hb_jd_preopt": DeHbJdPreopt,
}
APP_PREOPTIMIZATION_MODEL_MAP = {
    "app_hb_preopt": AppHbPreopt,
}

FILE_SCHEMAS = {
    "sqft": {
        "Territory": "territory_nm",
        "Store": "loc_cd",
        "Group": "grp_nm",
        "Dept": "dpt_nm",
        "Class": "clss_nm",
        "Subclass": "sub_clss_nm",
        "Sqft": "sqft",
    },
    "mdq": {
        "Territory": "territory_nm",
        "Store": "loc_cd",
        "Group": "grp_nm",
        "Dept": "dpt_nm",
        "Class": "clss_nm",
        "Subclass": "sub_clss_nm",
        "MDQ": "mdq",
    },
    "cover": {
        "Territory": "territory_nm",
        "Store": "loc_cd",
        "Group": "grp_nm",
        "Dept": "dpt_nm",
        "Class": "clss_nm",
        "Subclass": "sub_clss_nm",
        "Mid cover start (days)": "mid_cover_start",
        "High cover start (days)": "high_cover_start",
        "Depth": "depth",
    },
    "exclusion": {
        "Store": "loc_cd",
        "Group": "grp_nm",
        "Dept": "dpt_nm",
        "Class": "clss_nm",
        "Subclass": "sub_clss_nm",
    }
}

# ------------------------
# Map file_type to model
# ------------------------
FILE_MODELS = {
    "sqft": SqftFileData,
    "mdq": MdqFileData,
    "cover": CoverFileData,
    "exclusion": ExclusionFileData,
}

OPTIMIZATION_SUMMARY_MODEL_MAP = {
    "hb_optimization_summary": HbOptimizationSummary,
    "ls_optimization_summary": LsOptimizationSummary,
    "sp_optimization_summary": SpOptimizationSummary,
}
SAT_PLOT_MODEL_MAP = {
    "ds_hb_sat_plot": DsHbSatPlot,
    "ds_ls_sat_plot": DsLsSatPlot,
    "ds_sp_sat_plot": DsSpSatPlot,
}

APP_PREOPT_MODEL_MAP = {
    "app_hb_preopt": AppHbPreopt,
    "app_ls_preopt": AppLsPreopt,
    "app_sp_preopt": AppSpPreopt,
}

PERFORMANCE_MODEL_MAP = {
    "ds_hb_performance_ref": DeHbPerformanceRef,
    "ds_ls_performance_ref": DeLsPerformanceRef,
    "ds_sp_performance_ref": DeSpPerformanceRef,
}

metric_map = {
    "GMV": "GMV",
    "REVENUE": "REV"
}
UPLIFT_MAP = {
    "hb_evaluation_metrics": HbEvaluationMetrics,
    "ls_evaluation_metrics": LsEvaluationMetrics,
    "sp_evaluation_metrics": SpEvaluationMetrics,
}

RANGE_UPLIFT_MAP = {
    "hb_range_evaluation_metrics": HbRangeEvaluationMetrics,
    "ls_range_evaluation_metrics": LsRangeEvaluationMetrics,
    "sp_range_evaluation_metrics": SpRangeEvaluationMetrics,
}

DS_PRERANGEOPTIMIZATION_MODEL_MAP = {
    "hb_ae_pre_range_optimizer": HbAePreRangeOptimizer,
    "hb_ks_pre_range_optimizer": HbKsPreRangeOptimizer,
}