from collections import defaultdict
import csv
from django.core.paginator import <PERSON>gin<PERSON>
from django.db import connection
import requests
from rest_framework.views import APIView
from rest_framework.response import Response
import math
import json
import os
import openpyxl
from rest_framework import status
from .serializers import (
    PreoptDataSerializer, PreoptRequestSerializer, PreoptUpdateSerializer, ScenarioMetadSerializer,ClusterDataSerializer, OutlierDetectionResponseSerializer,OutlierDetectionRequestSerializer,
    ClusterRequestSerializer,testAndControlStoreSerializer,InsertTestControlStrRequestSerializer,InsertTestControlStrResponseSerializer,GraphDataRequestSerializer,GraphDataResponseSerializer,MetricGraphResponseSerializer,MetricGraphRequestSerializer,GDCSDataRequestSerializer,
    OutlierUpdateSerializer
)
from .model_map import (
    APP_PREOPTIMIZATION_MODEL_MAP, CLUSTER_MODEL_MAP, RANGE_UPLIFT_MAP, STR_CLUSTER_MODEL_MAP,
    DS_PRERANGEOPTIMIZATION_MODEL_MAP
    )
from .models import ScenarioStatus, FileUpload, AppHbPreopt
from .serializers import ScenarioMetadSerializer, ClusterDataSerializer, ClusterUpdateSerializer
from django.utils import timezone
from django.db import transaction
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from rest_framework.exceptions import ValidationError
from django.http import HttpResponse, JsonResponse
from .model_map import CLUSTER_MODEL_MAP, DE_PREOPTIMIZATION_MODEL_MAP,APP_PREOPTIMIZATION_MODEL_MAP,FILE_SCHEMAS,FILE_MODELS, OPTIMIZATION_SUMMARY_MODEL_MAP, SAT_PLOT_MODEL_MAP,APP_PREOPT_MODEL_MAP,PERFORMANCE_MODEL_MAP,metric_map, UPLIFT_MAP
from .models import ScenarioStatus, ScenarioMetad, FileUploads, SqftFileData, MdqFileData, CoverFileData, ExclusionFileData, AdjustedDataHB, Territory

from .serializers import ScenarioMetadSerializer, ClusterDataSerializer,StoreSelectionDropdownSerializer,StoreSelectionDataSerializer
from dateutil.relativedelta import relativedelta
from datetime import datetime
from django.db.models.functions import Round, NullIf
from rest_framework.pagination import PageNumberPagination
import pandas as pd
import numpy as np
from decimal import Decimal, InvalidOperation
from django.db.models import Q, Sum, Avg, Max, F, Min, FloatField, ExpressionWrapper,  Subquery, OuterRef, Exists
from .outlier_detection_service import OutlierDetectionService
from .services import HealthMetricsProcessing,SummaryTotalCalculator

import cProfile, pstats, io
from functools import wraps
from django.http import JsonResponse
from dateutil import parser




def profile_api(view_func):
    @wraps(view_func)
    def _wrapped_view(*args, **kwargs):
        pr = cProfile.Profile()
        pr.enable()
        response = view_func(*args, **kwargs)   # run your view
        pr.disable()

        # collect profiling stats
        s = io.StringIO()
        ps = pstats.Stats(pr, stream=s).sort_stats('cumulative')
        ps.print_stats(30)  # top 20 functions

        # log in console
        print(s.getvalue())

        # optionally return profiling info along with response
        if isinstance(response, JsonResponse):
            data = response.json()
            data["_profile"] = s.getvalue()
            return JsonResponse(data)
        return response
    return _wrapped_view


class SpaceHealthView(APIView):

    def get(self, request):
        concept = request.query_params.get("concept")
        # month = request.query_params.get("month")
        


        if not concept:
            return Response({"error": "Missing required parameters: concept"}, status=400)

        model = PREOPT_MODEL_MAP.get(concept.lower())

        if not model:
            return Response({"error": f"Invalid concept: {concept}"}, status=400)

        # Fetch the data
        try:
            data = model.objects.values(
                "grp_nm", "dpt_nm", "clss_nm", "sub_clss_nm",
                "loc_cd", "loc_nm",
                "total_lm", "spc", "gmv", "net_sls_amt",
                "mnth_avg_soh", "mnth_avg_optn_cnt", "ros", "cover",
                "gmv_per_lm", "lm_contribution_in_store"
            )
        except Exception as e:
            return Response({"error": str(e)}, status=500)

        # Now build the nested response
        result = []
        group_map = defaultdict(lambda: {"children": []})

        for row in data:
            group = row["grp_nm"] or "-"
            department = row["dpt_nm"] or "-"
            class_name = row["clss_nm"] or "-"
            sub_class = row["sub_clss_nm"] or "-"

            # Build keys for each level
            grp = group_map[group]
            grp.update({"group": group})
            dept_list = grp.setdefault("children", [])
            
            # Find or create department
            dept = next((d for d in dept_list if d["department"] == department), None)
            if not dept:
                dept = {"department": department, "children": []}
                dept_list.append(dept)

            # Find or create class
            class_list = dept["children"]
            cls = next((c for c in class_list if c["class"] == class_name), None)
            if not cls:
                cls = {"class": class_name, "children": []}
                class_list.append(cls)

            # Subclass data
            cls["children"].append({
                "subClass": sub_class,
                "storeId": row["loc_cd"],
                "storeName": row["loc_nm"],
                "lm": row["total_lm"],
                "sqft": row["spc"],
                "gmv": row["gmv"],
                "revenue": row["net_sls_amt"],
                "avgStock": row["mnth_avg_soh"],
                "optionCount": row["mnth_avg_optn_cnt"],
                "presentRos": row["ros"],
                "coverDays": row["cover"],
                "gmvPerLm": row["gmv_per_lm"],
                "lmCont": row["lm_contribution_in_store"],
            })

        result = list(group_map.values())
        return Response(result, status=200)
    

from .services import HealthMetricsProcessing

class StoreListView(APIView):

    def get(self, request):
        concept = request.query_params.get("concept")
        territory = request.query_params.get("territory")
        
        print("concept and territory ", concept, territory)
        if not concept or not territory:
            return Response({"error": "Missing required parameters: concept and territory"}, status=400)

        
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        try:
            queryset = ClusterModel.objects.all().values("loc_cd", "loc_nm")
            return Response(list(queryset), status=200)
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

class HeallthMetricsPagination(PageNumberPagination):
    """Custom pagination for HeallthMetrics endpoint with page size of 10"""
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

def parse_month_year_to_datetime(value):
    # Convert "YYYY-MM" to datetime object "YYYY-MM-01 00:00:00"
    if value and isinstance(value, str) and len(value) == 7:
        try:
            return datetime.strptime(value + '-01', '%Y-%m-%d')
        except ValueError:
            return None
    return None

class ScenarioCreateAPIView(APIView):

    def post(self, request):
        try:
            with transaction.atomic():
                data = request.data.copy()

            user_id = getattr(getattr(request, "user", None), "id", None)
            if not data.get("user_id") and user_id:
                data["user_id"] = user_id

                uploaded_files = {}

                # Handle sqft file
                if data.get("sqft_file_id"):
                    uploaded_files["sqft_file"] = data.get("sqft_file_id")
                if data.get("sqft_filename"):
                    uploaded_files["sqft_filename"] = data.get("sqft_filename")

                # Handle cover file
                if data.get("cover_file_id"):
                    uploaded_files["cover_file"] = data.get("cover_file_id")
                if data.get("cover_filename"):
                    uploaded_files["cover_filename"] = data.get("cover_filename")

                # Handle mdq file
                if data.get("mdq_file_id"):
                    uploaded_files["mdq_file"] = data.get("mdq_file_id")
                if data.get("mdq_filename"):
                    uploaded_files["mdq_filename"] = data.get("mdq_filename")

                # Handle exclusion file
                if data.get("exclusion_file_id"):
                    uploaded_files["exclusion_file"] = data.get("exclusion_file_id")
                    if data.get("exclusion_filename"):
                        uploaded_files["exclusion_filename"] = data.get("exclusion_filename")

                data.update(uploaded_files)

                DEFAULT_WEIGHTS = {
                    "cover": 0.15,
                    "gmv_margin_pct": 0.25,
                    "gmv_per_lm": 0.25,
                    "sub_cpen": 0.1,
                    "d_units": 0.15,
                    "asp": 0.1
                }

                data["weights"] = json.dumps(DEFAULT_WEIGHTS)

                # Parse dates
                for date_field in ['ref_start', 'ref_end']:
                    if date_field in data:
                        converted = parse_month_year_to_datetime(data[date_field])
                        data[date_field] = converted.date() if converted else None

                scenario_id = data.get('id')

                if scenario_id:
                    # 🔁 Update existing scenario
                    try:
                        instance = ScenarioMetad.objects.get(id=scenario_id)
                    except ScenarioMetad.DoesNotExist:
                        return Response({'error': f'Scenario with id {scenario_id} not found'}, status=404)
                  
                    serializer = ScenarioMetadSerializer(instance, data=data, partial=True)
                else:
                    # 🆕 Create new scenario
                    serializer = ScenarioMetadSerializer(data=data)

                try:
                    serializer.is_valid(raise_exception=True)
                except ValidationError as e:
                    print("Validation errors:", serializer.errors)
                    return Response({"errors": serializer.errors}, status=400)

                scenario = serializer.save(updated_at=timezone.now())
                
                try:
                    if scenario.sqft_file:
                        SqftFileData.objects.filter(file_upload_id=scenario.sqft_file.id).update(scenario_id=scenario.id, STND_TRRTRY_NM = scenario.TERRITORY_NM)
                    if scenario.mdq_file:
                        MdqFileData.objects.filter(file_upload_id=scenario.mdq_file.id).update(scenario_id=scenario.id, STND_TRRTRY_NM = scenario.TERRITORY_NM)
                    if scenario.cover_file:
                        CoverFileData.objects.filter(file_upload_id=scenario.cover_file.id).update(scenario_id=scenario.id, STND_TRRTRY_NM = scenario.TERRITORY_NM)
                    if scenario.exclusion_file_id:
                        ExclusionFileData.objects.filter(file_upload_id=scenario.exclusion_file_id).update(scenario_id=scenario.id, STND_TRRTRY_NM = scenario.TERRITORY_NM)
                except Exception as e:
                    return Response({"error": f"Failed to update file data tables with scenario_id:: {str(e)}"}, status=500)

                if not scenario_id:
                    # Only create status record on first creation
                    ScenarioStatus.objects.create(scenario=scenario)

                # Prepare detailed response with all saved data
                response_data = {
                    'message': f'Scenario {"updated" if scenario_id else "created"} successfully',
                    'scenario_details': {
                        'scenario_id': scenario.id,
                        'name': scenario.name,
                        'user_id': scenario.user_id,
                        'season_type': scenario.season_type,
                        'eval_type': scenario.eval_type,
                        'event_name': scenario.event_name,
                        'eval_start': scenario.eval_start.isoformat() if scenario.eval_start else None,
                        'eval_end': scenario.eval_end.isoformat() if scenario.eval_end else None,
                        'ref_start': scenario.ref_start.isoformat() if scenario.ref_start else None,
                        'ref_end': scenario.ref_end.isoformat() if scenario.ref_end else None,
                        'concept_name': scenario.CNCPT_NM,
                        'territory_name': scenario.TERRITORY_NM,
                        'metric': scenario.metric,
                        'location_codes': scenario.loc_cd,
                        'sqft_file_id': scenario.sqft_file_id,
                        'mdq_file_id': scenario.mdq_file_id,
                        'cover_file_id': scenario.cover_file_id,
                        'exclusion_file_id': scenario.exclusion_file_id,
                        'sqft_filename': scenario.sqft_filename,
                        'mdq_filename': scenario.mdq_filename,
                        'cover_filename': scenario.cover_filename,
                        'exclusion_filename': scenario.exclusion_filename,
                        'weights': scenario.weights,
                        'created_by': scenario.created_by,
                        'updated_by': scenario.updated_by,
                        'created_at': scenario.created_at.isoformat() if scenario.created_at else None,
                        'updated_at': scenario.updated_at.isoformat() if scenario.updated_at else None
                    },
                    }
                    
                

                return Response(response_data, status=200 if scenario_id else 201)

        except Exception as e:
            import traceback
            traceback_str = traceback.format_exc()
            print("Exception traceback:", traceback_str)
            return Response({'error': str(e), 'traceback': traceback_str}, status=400)



class ClusterByLocationView(APIView):
    def post(self, request):
        input_serializer = ClusterRequestSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]
        scenario_id = request.data.get("scenario_id")
        try:
            territory_name = territory.upper()
        except Exception as e:
            return Response({"error": "Territory name is not str."}, status=500)
        # Step 2: Get raw model (de_*_cluster) for source data
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        target_table = f"app_{concept.lower()}_str_cluster"

        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        AppClusterModel = STR_CLUSTER_MODEL_MAP.get(target_table)
        if not ClusterModel or not AppClusterModel:
            return Response({"error": "Invalid source or target table."}, status=400)

        # Step 3: Fetch source data (de_* cluster → copy to app_* cluster)
        try:
            source_data = ClusterModel.objects.all()
            with transaction.atomic():
                for row in source_data:
                    data_dict = row.__dict__.copy()
                    data_dict.pop("_state", None)
                    data_dict.pop("id", None)

                    data_dict["scenario_id"] = scenario_id
                    data_dict["new_cluster_num"] = row.cluster_num
                    data_dict["territory_nm"] = territory_name
                    data_dict["stnd_trrtry_nm"] = territory_name

                    exists = AppClusterModel.objects.filter(
                            scenario_id=scenario_id,
                            loc_cd=data_dict.get("loc_cd")
                            # cluster_num=row.cluster_num
                    ).exists()

                    if not exists:
                        AppClusterModel.objects.create(**data_dict)

            # Step 5: Fetch from the app_* table using scenario_id
            filtered_data = AppClusterModel.objects.filter(scenario_id=scenario_id)

            # Step 6: Group and serialize
            grouped = defaultdict(list)
            for row in filtered_data:
                ClusterDataSerializer.Meta.model = AppClusterModel
                serialized = ClusterDataSerializer(row).data
                grouped[row.new_cluster_num].append(serialized)

            return Response(grouped)
        except Exception as e:
            print("error is ", e)
            return Response({"error": f"Database error: {str(e)}"}, status=500)


    
    def put(self, request):
        
        updates = request.data.get('updates', [])
        
        serializer = ClusterUpdateSerializer(data=updates, many=True)
        
        if not serializer.is_valid(): 
            return Response(serializer.errors, status=400)

        
        validated_updates = serializer.validated_data

        # Step 2: Determine the model to update (similar to post method)
        

        # user_id = request.user.id
        timestamp = timezone.now()
       
        try:
            with transaction.atomic():
                for update in validated_updates:
                    loc_cd = update['loc_cd']
                    new_cluster_num = update['cluster_num']
                    concept = update['concept']
                    scenario_id = update['scenario_id']
                    
                    if not concept:
                        return Response({"error": "concept and territory query params are required"}, status=400)
                    str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
                    if not str_model:
                        return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)
                    # Update the cluster_num and new_cluster_num fields
                    try:
                        str_model.objects.filter(loc_cd=loc_cd,scenario_id= int(scenario_id)).update(
                            new_cluster_num=new_cluster_num,
                            updated_at=timestamp,
                            # updated_by=1
                        )
                    except Exception as e:
                        print("error in saving", e)
        except Exception as e:
            return Response({"error": f"Update failed: {str(e)}"}, status=500)

        return Response({"detail": "Clusters updated successfully"})


    def delete(self, request):
        loc_cd = request.data.get("loc_cd")
        cluster_num = request.data.get("cluster_num")
        concept = request.data.get("concept")

        
        if not all([loc_cd, concept]):
            return Response({"error": "loc_cd, cluster_num, and concept are required"}, status=400)

        str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
        if not str_model:
            return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)

        try:
            deleted_count, _ = str_model.objects.filter(
                loc_cd=loc_cd,
                cluster_num=cluster_num
            ).delete()

            if deleted_count == 0:
                return Response({"detail": "No matching record found."}, status=404)

            return Response({"detail": f"{deleted_count} store(s) deleted."}, status=200)

        except Exception as e:
            return Response({"error": str(e)}, status=500)
        
class PreOptByLocation(APIView):
    def post(self, request):
        input_serializer = PreoptRequestSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]
        scenario_id = validated["scenario_id"]
        performance_metric = validated.get("performance_metric").lower()

        table_name = f"de_{concept.lower()}_{territory.lower()}_preopt"
        preOptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(table_name)
        app_table_name = f"app_{concept.lower()}_preopt"
        AppPreoptModel = APP_PREOPT_MODEL_MAP.get(app_table_name)
        if not preOptModel:
            return Response({"error": f"Unsupported preopt table: {table_name}"}, status=400)

        source_data = preOptModel.objects.values_list(
            'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm', 'loc_cd', 'month',
            'total_lm', 'gmv_outlier_status', 'gmv_suggested_total_lm',
            'rev_outlier_status', 'rev_suggested_total_lm'
        )

        deleted_count, _ = AppPreoptModel.objects.filter(scenario_id=scenario_id).delete()

        existing_records = set(
            AppPreoptModel.objects.filter(scenario_id=scenario_id).values_list(
                "grp_nm", "dpt_nm", "clss_nm", "sub_clss_nm", "loc_cd", "month"
            )
        )
        objs_to_insert = []
        for row in source_data:
            row_key = (row[0], row[1], row[2], row[3], row[4], row[5])
            if row_key not in existing_records:
                objs_to_insert.append(
                    AppPreoptModel(
                        scenario_id=scenario_id,
                        grp_nm=row[0],
                        dpt_nm=row[1],
                        clss_nm=row[2],
                        sub_clss_nm=row[3],
                        loc_cd=row[4],
                        month=row[5],
                        total_lm=row[6],
                        outlier_status_final=row[9] if performance_metric == "revenue" else row[7],
                        gmv_outlier_status=row[7],
                        gmv_suggested_total_lm=row[8],
                        rev_outlier_status=row[9],
                        rev_suggested_total_lm=row[10],
                    )
                )

        try:
            with transaction.atomic():
                AppPreoptModel.objects.bulk_create(
                    objs_to_insert, batch_size=1000, ignore_conflicts=True
                )
        except Exception as e:
            return Response({"error": f"Insert failed: {str(e)}"}, status=500)

        return Response({
            "concept": concept,
            "territory": territory,
            "inserted_rows": len(objs_to_insert),
            "deleted_rows": deleted_count
        })

class StoreSelectionDropdown(APIView):
    
    def post(self, request):

        input_serializer = StoreSelectionDropdownSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]

        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        try:
            filtered_data = ClusterModel.objects.all()
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)

        StoreSelectionDataSerializer.Meta.model = ClusterModel
        serializer = StoreSelectionDataSerializer(filtered_data, many=True)
        formatted_data = [
            {
                "value": item["loc_cd"],
                "label": f"{item['loc_cd']} - {item['loc_nm']}"
            }
            for item in serializer.data
        ]
      
        return Response(formatted_data)


    def put(self, request):
        # Step 1: Validate input list of updates
        
        updates = request.data.get('updates', [])
        
        serializer = ClusterUpdateSerializer(data=updates, many=True)
        
        if not serializer.is_valid():
            print("serializer errors:", serializer.errors) 
            return Response(serializer.errors, status=400)

        
        validated_updates = serializer.validated_data

        # Step 2: Determine the model to update (similar to post method)
        

        # user_id = request.user.id
        timestamp = timezone.now()
       
        try:
            with transaction.atomic():
                for update in validated_updates:
                    loc_cd = update['loc_cd']
                    new_cluster_num = update['cluster_num']
                    concept = update['concept']
                    
                    if not concept:
                        return Response({"error": "concept and territory query params are required"}, status=400)
                    str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
                    if not str_model:
                        return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)
                    # Update the cluster_num and new_cluster_num fields
                    try:
                        str_model.objects.filter(LOC_CD=loc_cd).update(
                            CLUSTER_NUM=new_cluster_num,
                            new_cluster_num=new_cluster_num,
                            updated_at=timestamp,
                            # updated_by=1
                        )
                    except Exception as e:
                        print("error in saving", e)
        except Exception as e:
            return Response({"error": f"Update failed: {str(e)}"}, status=500)

        return Response({"detail": "Clusters updated successfully"})


    def delete(self, request):
        loc_cd = request.data.get("loc_cd")
        cluster_num = request.data.get("cluster_num")
        concept = request.data.get("concept")

        
        if not all([loc_cd, concept]):
            return Response({"error": "loc_cd, cluster_num, and concept are required"}, status=400)

        str_model = STR_CLUSTER_MODEL_MAP.get(f"app_{concept.lower()}_str_cluster")
        if not str_model:
            return Response({"error": f"Unsupported cluster table for concept {concept}"}, status=400)

        try:
            deleted_count, _ = str_model.objects.filter(
                LOC_CD=loc_cd,
                CLUSTER_NUM=cluster_num
            ).delete()

            if deleted_count == 0:
                return Response({"detail": "No matching record found."}, status=404)

            return Response({"detail": f"{deleted_count} store(s) deleted."}, status=200)

        except Exception as e:
            return Response({"error": str(e)}, status=500)


class OutlierAPIView(APIView):
    def get(self, request):
        """
        Optimized: First fetch distinct filter/store values, then fetch data only for selected/first store.
        """
        try:
            performance_metric = request.query_params.get('performance_metric', 'GMV').upper()
            concept = request.query_params.get('concept')
            territory = request.query_params.get('territory')
            scenario_id = request.query_params.get('scenario_id')
            loc_cd = request.query_params.getlist("loc_cd[]") or request.query_params.get('loc_cd', '')
            cluster = request.query_params.getlist('cluster[]') or request.query_params.getlist('cluster')
            group = request.query_params.getlist('group[]')
            department = request.query_params.getlist('department[]')
            class_field = request.query_params.getlist('class_field[]')
            sub_class = request.query_params.getlist('sub_class[]')
            outlier_status = request.query_params.getlist('outlier_status')
            
            page = int(request.query_params.get('page', 1))
            per_page = int(request.query_params.get('limit', 10))

            offset = (page - 1) * per_page

            if not concept:
                return Response({'error': 'Missing concept parameter'}, status=400)

            # Table names
            de_table = f"de_{concept.lower()}_{territory.lower()}_preopt"
            app_table = f"app_{concept.lower()}_preopt"
            cluster_table = f"app_{concept.lower()}_str_cluster"

            # Dynamic columns
            if performance_metric == "GMV":
                outlier_status_col = "gmv_outlier_status"
                suggested_total_lm_col = "gmv_suggested_total_lm"
                per_day_col = "gmv_per_day"
            else:
                outlier_status_col = "rev_outlier_status"
                suggested_total_lm_col = "rev_suggested_total_lm"
                per_day_col = "rev_per_day"

            # 1. Get distinct filter/store values (fast query)
            filter_sql = f'''
                SELECT DISTINCT
                    de.grp_nm,
                    de.dpt_nm,
                    de.clss_nm,
                    de.sub_clss_nm,
                    cluster.new_cluster_num AS cluster_num,
                    de.loc_cd,
                    de.loc_nm
                FROM {de_table} de
                LEFT JOIN {app_table} app
                    ON de.grp_nm = app.grp_nm
                    AND de.dpt_nm = app.dpt_nm
                    AND de.clss_nm = app.clss_nm
                    AND de.sub_clss_nm = app.sub_clss_nm
                    AND de.loc_cd = app.loc_cd
                    AND de.month = app.month
                LEFT JOIN {cluster_table} cluster
                    ON app.loc_cd = cluster.loc_cd
                    where app.scenario_id = {scenario_id}
                    and cluster.scenario_id = {scenario_id}
                ORDER BY de.grp_nm, de.dpt_nm, de.clss_nm, de.sub_clss_nm, app.loc_cd, de.month
            '''

            with connection.cursor() as cursor:
                cursor.execute(filter_sql)
                filter_rows = cursor.fetchall()
                filter_columns = [col[0] for col in cursor.description]

            filter_values = set()
            store_values = set()
            for row in filter_rows:
                row_dict = dict(zip(filter_columns, row))
                filter_values.add((
                    row_dict.get('grp_nm'),
                    row_dict.get('dpt_nm'),
                    row_dict.get('clss_nm'),
                    row_dict.get('sub_clss_nm'),
                    row_dict.get('cluster_num'),
                    row_dict.get('loc_cd'),
                    row_dict.get('loc_nm')
                ))
                store_values.add((row_dict.get('loc_cd'), row_dict.get('loc_nm')))

            filter_list = [
                {
                    'grp_nm': f[0],
                    'dpt_nm': f[1],
                    'clss_nm': f[2],
                    'sub_clss_nm': f[3],
                    'cluster_num': f[4],
                    'loc_cd': f[5],
                    'loc_nm': f[6]
                }
                for f in sorted(filter_values)
            ]
            store_list = [
                {'loc_cd': s[0], 'loc_nm': s[1]} for s in sorted(store_values)
            ]


            # 2. Determine selected store(s)
            selected_loc_cds = []
            if isinstance(loc_cd, list) and loc_cd:
                selected_loc_cds = [str(l) for l in loc_cd if l]
            elif loc_cd:
                selected_loc_cds = [str(loc_cd)]
            elif store_list and len(store_list) > 0 and store_list[0].get('loc_cd') is not None:
                selected_loc_cds = [store_list[0]['loc_cd']]
            
            filter_conditions = []
            filter_params = []

            # Group filter
            if group:
                placeholders = ','.join(['%s'] * len(group))
                filter_conditions.append(f"de.grp_nm IN ({placeholders})")
                filter_params.extend(group)

            # Department filter
            if department:
                placeholders = ','.join(['%s'] * len(department))
                filter_conditions.append(f"de.dpt_nm IN ({placeholders})")
                filter_params.extend(department)

            # Class filter
            if class_field:
                placeholders = ','.join(['%s'] * len(class_field))
                filter_conditions.append(f"de.clss_nm IN ({placeholders})")
                filter_params.extend(class_field)

            # Sub-class filter
            if sub_class:
                placeholders = ','.join(['%s'] * len(sub_class))
                filter_conditions.append(f"de.sub_clss_nm IN ({placeholders})")
                filter_params.extend(sub_class)

            # Outlier status filter
            outlier_status = [status for status in outlier_status if status.strip()]

            if outlier_status:
                placeholders = ','.join(['%s'] * len(outlier_status))
                filter_conditions.append(f"app.{outlier_status_col} IN ({placeholders})")
                filter_params.extend(outlier_status)

            # Combine all additional filter conditions
            filter_sql_snippet = ""
            if filter_conditions:
                filter_sql_snippet = " AND " + " AND ".join(filter_conditions)

            # 3. Fetch data for all selected stores (fast)
            data = []
            if selected_loc_cds:
                # Build SQL IN clause for multiple stores
                loc_cd_placeholders = ','.join(['%s' for _ in selected_loc_cds])
                cluster_values = cluster if cluster else []

                cluster_filter_sql = ""
                cluster_filter_params = []

                if cluster_values:
                    placeholders = ','.join(['%s'] * len(cluster_values))
                    cluster_filter_sql = f" AND cluster.new_cluster_num IN ({placeholders}) "
                    cluster_filter_params = cluster_values

                count_sql = f'''
                    SELECT COUNT(*)
                    FROM {de_table} de
                    LEFT JOIN {app_table} app
                        ON de.grp_nm = app.grp_nm
                        AND de.dpt_nm = app.dpt_nm
                        AND de.clss_nm = app.clss_nm
                        AND de.sub_clss_nm = app.sub_clss_nm
                        AND de.loc_cd = app.loc_cd
                        AND de.month = app.month
                    LEFT JOIN {cluster_table} cluster
                        ON de.loc_cd = cluster.loc_cd
                        AND cluster.scenario_id = %s
                    WHERE app.scenario_id = %s AND app.loc_cd IN ({loc_cd_placeholders}) {cluster_filter_sql} {filter_sql_snippet}
                '''
                
                with connection.cursor() as cursor:
                    cursor.execute(count_sql, [scenario_id, scenario_id] + selected_loc_cds + cluster_filter_params + filter_params)
                    total_count = cursor.fetchone()[0]

                data_sql = f'''
                    SELECT
                        de.grp_nm,
                        de.dpt_nm,
                        de.clss_nm,
                        de.sub_clss_nm,
                        de.loc_nm,
                        de.month,
                        app.loc_cd,
                        de.total_lm,
                        de.lm_contribution_in_store,
                        de.{per_day_col} AS per_day,
                        app.outlier_status_final,
                        app.{outlier_status_col} AS outlier_status,
                        app.{suggested_total_lm_col} AS suggested_total_lm,
                        cluster.CLUSTER_NUM AS cluster_num,
                        cluster.new_cluster_num AS new_cluster_num
                    FROM {de_table} de
                    LEFT JOIN {app_table} app
                        ON de.grp_nm = app.grp_nm
                        AND de.dpt_nm = app.dpt_nm
                        AND de.clss_nm = app.clss_nm
                        AND de.sub_clss_nm = app.sub_clss_nm
                        AND de.loc_cd = app.loc_cd
                        AND de.month = app.month
                    LEFT JOIN {cluster_table} cluster
                        ON de.loc_cd = cluster.loc_cd
                        AND cluster.scenario_id = {scenario_id}
                    WHERE app.scenario_id = {scenario_id} AND app.loc_cd IN ({loc_cd_placeholders}) {cluster_filter_sql}  {filter_sql_snippet}
                    ORDER BY de.grp_nm, de.dpt_nm, de.clss_nm, de.sub_clss_nm, app.loc_cd, de.month
                    LIMIT %s OFFSET %s
                '''
                
                with connection.cursor() as cursor:
                    params = selected_loc_cds + cluster_filter_params + filter_params  + [per_page, offset]
                    cursor.execute(data_sql, params)
                    columns = [col[0] for col in cursor.description]
                    rows = cursor.fetchall()
                data = [dict(zip(columns, row)) for row in rows]

            return Response({
                'count': total_count,
                'data': data,
                'filters': filter_list,
                'stores': store_list,
                'selected_loc_cd': selected_loc_cds
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    

    def patch(self, request):
        """
        Handles both individual and bulk updates to `outlier_status_final` in app_{concept}_preopt table.
        For individual updates, expects a list of records.
        For bulk approval, expects a dict with `bulk_approve: true` and relevant filter criteria.
        """

        data = request.data
        if isinstance(data, dict) and data.get("bulk_approve"):
            return self._handle_bulk_approval(data)
        elif isinstance(data, list):
            return self._handle_individual_updates(data)
        else:
            return Response({
                'error': 'Invalid input format',
                'details': 'Expected list for individual updates or object with bulk_approve: true'
            }, status=status.HTTP_400_BAD_REQUEST)


    def _handle_individual_updates(self, data):
        updated_records = []
        failed_records = []

        for index, item in enumerate(data):
            concept = item.get('concept')
            sub_clss_nm = item.get('sub_clss_nm')
            loc_cd = item.get('loc_cd')
            month = item.get('month')
            scenario_id = item.get('scenario_id')
            outlier_status_final = item.get('outlier_status_final')
            update_fields = {"outlier_status_final": outlier_status_final}

            if "gmv_suggested_total_lm" in item and item["gmv_suggested_total_lm"] is not None:
                update_fields["total_lm"] = item["gmv_suggested_total_lm"]
            elif "rev_suggested_total_lm" in item and item["rev_suggested_total_lm"] is not None:
                update_fields["total_lm"] = item["rev_suggested_total_lm"]

            if not all([concept, sub_clss_nm, loc_cd, month, scenario_id, outlier_status_final]):
                failed_records.append({
                    'index': index,
                    'error': 'Missing required fields',
                    'data': item
                })
                continue

            app_table_name = f"app_{concept.lower()}_preopt"
            AppPreoptModel = APP_PREOPT_MODEL_MAP.get(app_table_name)
            if not AppPreoptModel:
                failed_records.append({
                    'index': index,
                    'error': f'Unsupported concept: {concept}',
                    'data': item
                })
                continue

            try:
                updated = AppPreoptModel.objects.filter(
                    scenario_id=scenario_id,
                    sub_clss_nm=sub_clss_nm,
                    loc_cd=loc_cd,
                    month=month,
                    outlier_status_final='MAJOR_OUTLIER'
                ).update(**update_fields)
                if updated:
                    updated_records.append({
                        'sub_clss_nm': sub_clss_nm,
                        'loc_cd': loc_cd,
                        'month': month,
                        'scenario_id': scenario_id,
                        **update_fields
                    })
                else:
                    failed_records.append({
                        'index': index,
                        'error': 'Record not found',
                        'data': item
                    })
            except Exception as e:
                failed_records.append({
                    'index': index,
                    'error': str(e),
                    'data': item
                })

        return Response({
            'updated_count': len(updated_records),
            'updated_records': updated_records,
            'failed_count': len(failed_records),
            'failed_records': failed_records
        }, status=status.HTTP_200_OK)


    def _handle_bulk_approval(self, data):
        
        concept = data.get('concept')
        scenario_id = data.get('scenario_id')
        loc_cd = data.get('loc_cd')
        new_cluster_num = data.get('cluster')
        outlier_status_final = data.get('final_status')

        if not all([concept, scenario_id, loc_cd, new_cluster_num, outlier_status_final]):
            return Response({
                'error': 'Missing required bulk update parameters'
            }, status=status.HTTP_400_BAD_REQUEST)

        app_table_name = f"app_{concept.lower()}_preopt"
        AppPreoptModel = APP_PREOPT_MODEL_MAP.get(app_table_name)

        if not AppPreoptModel:
            return Response({
                'error': f'Unsupported concept: {concept}'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Build dynamic filters
        filters = {
            'scenario_id': scenario_id,
            'outlier_status_final':'MAJOR_OUTLIER'
        }

        if isinstance(loc_cd, list):
            filters['loc_cd__in'] = loc_cd
        else:
            filters['loc_cd'] = loc_cd

        # Optional filters - add if present and not empty
        optional_filters = ['group', 'department', 'class', 'subclass']
        for key in optional_filters:
            value = data.get(key)
            if value:
                # Assuming the value can be a list or single value, adjust accordingly
                if isinstance(value, list):
                    filters[f"{key}__in"] = value
                else:
                    filters[key] = value

        try:
            updated_count = AppPreoptModel.objects.filter(**filters).update(
                outlier_status_final=outlier_status_final
            )
            return Response({
                'updated_count': updated_count,
                'message': 'Bulk approval update successful'
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Bulk update failed',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



class GetOutlierVisualizationAPIView(APIView):
    def get(self, request):
        """
        Optimized: First fetch distinct filter/store values, then fetch data only for selected/first store.
        """
        try:
            performance_metric = request.query_params.get('performance_metric', 'GMV').upper()
            concept = request.query_params.get('concept')
            territory = request.query_params.get('territory')
            scenario_id = request.query_params.get('scenario_id')
            loc_cd = request.query_params.getlist("loc_cd[]") or request.query_params.get('loc_cd', '')
            cluster = request.query_params.getlist('cluster[]') or request.query_params.get('cluster')
            
            sub_class = request.query_params.getlist('sub_class')

            if not concept:
                return Response({'error': 'Missing concept parameter'}, status=400)

            # Table names
            de_table = f"de_{concept.lower()}_{territory.lower()}_preopt"
            app_table = f"app_{concept.lower()}_preopt"
            cluster_table = f"app_{concept.lower()}_str_cluster"

            # Dynamic columns
            if performance_metric == "GMV":
                outlier_status_col = "gmv_outlier_status"
                suggested_total_lm_col = "gmv_suggested_total_lm"
                per_day_col = "gmv_per_day"
            else:
                outlier_status_col = "rev_outlier_status"
                suggested_total_lm_col = "rev_suggested_total_lm"
                per_day_col = "rev_per_day"

            # 1. Get distinct filter/store values (fast query)
            filter_sql = f'''
                SELECT DISTINCT
                    de.grp_nm,
                    de.dpt_nm,
                    de.clss_nm,
                    de.sub_clss_nm,
                    cluster.new_cluster_num AS cluster_num,
                    de.loc_cd,
                    de.loc_nm
                FROM {de_table} de
                LEFT JOIN {app_table} app
                    ON de.grp_nm = app.grp_nm
                    AND de.dpt_nm = app.dpt_nm
                    AND de.clss_nm = app.clss_nm
                    AND de.sub_clss_nm = app.sub_clss_nm
                    AND de.loc_cd = app.loc_cd
                    AND de.month = app.month
                LEFT JOIN {cluster_table} cluster
                    ON app.loc_cd = cluster.loc_cd
                    where app.scenario_id = {scenario_id}
            '''

            with connection.cursor() as cursor:
                cursor.execute(filter_sql)
                filter_rows = cursor.fetchall()
                filter_columns = [col[0] for col in cursor.description]

            filter_values = set()
            store_values = set()
            for row in filter_rows:
                row_dict = dict(zip(filter_columns, row))
                filter_values.add((
                    row_dict.get('grp_nm'),
                    row_dict.get('dpt_nm'),
                    row_dict.get('clss_nm'),
                    row_dict.get('sub_clss_nm'),
                    row_dict.get('cluster_num'),
                    row_dict.get('loc_cd'),
                    row_dict.get('loc_nm')
                ))
                store_values.add((row_dict.get('loc_cd'), row_dict.get('loc_nm')))

            filter_list = [
                {
                    'grp_nm': f[0],
                    'dpt_nm': f[1],
                    'clss_nm': f[2],
                    'sub_clss_nm': f[3],
                    'cluster_num': f[4],
                    'loc_cd': f[5],
                    'loc_nm': f[6]
                }
                for f in sorted(filter_values)
            ]
            store_list = [
                {'loc_cd': s[0], 'loc_nm': s[1]} for s in sorted(store_values)
            ]


            # 2. Determine selected store(s)
            selected_loc_cds = []
            if isinstance(loc_cd, list) and loc_cd:
                selected_loc_cds = [str(l) for l in loc_cd if l]
            elif loc_cd:
                selected_loc_cds = [str(loc_cd)]
            elif store_list and len(store_list) > 0 and store_list[0].get('loc_cd') is not None:
                selected_loc_cds = [store_list[0]['loc_cd']]
            
            filter_conditions = []
            filter_params = []


            # Sub-class filter
            if sub_class:
                placeholders = ','.join(['%s'] * len(sub_class))
                filter_conditions.append(f"de.sub_clss_nm IN ({placeholders})")
                filter_params.extend(sub_class)

            # Outlier status filter
            

            # Combine all additional filter conditions
            filter_sql_snippet = ""
            if filter_conditions:
                filter_sql_snippet = " AND " + " AND ".join(filter_conditions)

            # 3. Fetch data for all selected stores (fast)
            data = []
            if selected_loc_cds:
                # Build SQL IN clause for multiple stores
                loc_cd_placeholders = ','.join(['%s' for _ in selected_loc_cds])
                cluster_values = [cluster]

                cluster_filter_sql = ""
                cluster_filter_params = []

                if cluster_values:
                    placeholders = ','.join(['%s'] * len(cluster_values))
                    cluster_filter_sql = f" AND cluster.new_cluster_num IN ({placeholders}) "
                    cluster_filter_params = cluster_values


                data_sql = f'''
                    SELECT
                        de.grp_nm,
                        de.dpt_nm,
                        de.clss_nm,
                        de.sub_clss_nm,
                        de.loc_nm,
                        de.month,
                        app.loc_cd,
                        de.total_lm,
                        de.lm_contribution_in_store,
                        de.{per_day_col} AS per_day,
                        app.outlier_status_final,
                        app.{outlier_status_col} AS outlier_status,
                        app.{suggested_total_lm_col} AS suggested_total_lm,
                        cluster.CLUSTER_NUM AS cluster_num,
                        cluster.new_cluster_num AS new_cluster_num
                    FROM {de_table} de
                    LEFT JOIN {app_table} app
                        ON de.grp_nm = app.grp_nm
                        AND de.dpt_nm = app.dpt_nm
                        AND de.clss_nm = app.clss_nm
                        AND de.sub_clss_nm = app.sub_clss_nm
                        AND de.loc_cd = app.loc_cd
                        AND de.month = app.month
                    LEFT JOIN {cluster_table} cluster
                        ON de.loc_cd = cluster.loc_cd
                        AND cluster.scenario_id = {scenario_id}
                    WHERE app.scenario_id = {scenario_id} AND app.loc_cd IN ({loc_cd_placeholders}) {cluster_filter_sql}  {filter_sql_snippet}
                '''

                with connection.cursor() as cursor:
                    params = selected_loc_cds + cluster_filter_params + filter_params
                    cursor.execute(data_sql, params)
                    columns = [col[0] for col in cursor.description]
                    rows = cursor.fetchall()
                data = [dict(zip(columns, row)) for row in rows]

            return Response({
                'data': data,
                'filters': filter_list,
                'stores': store_list,
                'selected_loc_cd': selected_loc_cds
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': 'Internal server error',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def patch(self, request):
        """
        Update outlier_status_final in app_{concept}_preopt table for each record in request body
        """
        data = request.data
        if not data:
            return Response({
                'error': 'Invalid input data',
                'details': 'Request data cannot be empty'
            }, status=status.HTTP_400_BAD_REQUEST)

        updated_records = []
        failed_records = []

        for index, item in enumerate(data):
            concept = item.get('concept')
            sub_clss_nm = item.get('sub_clss_nm')
            loc_cd = item.get('loc_cd')
            month = item.get('month')
            scenario_id = item.get('scenario_id')
            outlier_status_final = item.get('outlier_status_final')
            update_fields = {"outlier_status_final": outlier_status_final}

            if "gmv_suggested_total_lm" in item and item["gmv_suggested_total_lm"] is not None:
                update_fields["total_lm"] = item["gmv_suggested_total_lm"]
            elif "rev_suggested_total_lm" in item and item["rev_suggested_total_lm"] is not None:
                update_fields["total_lm"] = item["rev_suggested_total_lm"]
   
            if not all([concept, sub_clss_nm, loc_cd, month, scenario_id, outlier_status_final]):
                failed_records.append({
                    'index': index,
                    'error': 'Missing required fields',
                    'data': item
                })
                continue

            app_table_name = f"app_{concept.lower()}_preopt"
            AppPreoptModel = APP_PREOPT_MODEL_MAP.get(app_table_name)
            if not AppPreoptModel:
                failed_records.append({
                    'index': index,
                    'error': f'Unsupported concept: {concept}',
                    'data': item
                })
                continue

            try:
                updated = AppPreoptModel.objects.filter(
                    scenario_id=scenario_id,
                    sub_clss_nm=sub_clss_nm,
                    loc_cd=loc_cd,
                    month=month
                ).update(**update_fields)
                if updated:
                    updated_records.append({
                        'sub_clss_nm': sub_clss_nm,
                        'loc_cd': loc_cd,
                        'month': month,
                        'scenario_id': scenario_id,
                        **update_fields
                    })
                else:
                    failed_records.append({
                        'index': index,
                        'error': 'Record not found',
                        'data': item
                    })
            except Exception as e:
                failed_records.append({
                    'index': index,
                    'error': str(e),
                    'data': item
                })

        return Response({
            'updated_count': len(updated_records),
            'updated_records': updated_records,
            'failed_count': len(failed_records),
            'failed_records': failed_records
        }, status=status.HTTP_200_OK)


class DownloadOutlierData(APIView):
    def get(self, request):
        try:
            
            performance_metric = request.query_params.get('performance_metric', 'GMV').upper()
            concept = request.query_params.get('concept')
            territory = request.query_params.get('territory')
            scenario_id = request.query_params.get('scenario_id')
            loc_cd = request.query_params.getlist("loc_cd[]") or request.query_params.getlist('loc_cd')
            # cluster = request.query_params.getlist('cluster[]') or request.query_params.getlist('cluster')
            # group = request.query_params.getlist('group[]') or request.query_params.getlist('group')
            # department = request.query_params.getlist('department[]') or request.query_params.getlist('department')
            # class_field = request.query_params.getlist('class_field[]') or request.query_params.getlist('class_field')
            # sub_class = request.query_params.getlist('sub_class[]') or request.query_params.getlist('sub_class')
            # outlier_status = request.query_params.getlist('outlier_status[]') or request.query_params.getlist('outlier_status')
            
            if not concept:
                return Response({'error': 'Missing concept parameter'}, status=400)
            if not territory:
                return Response({'error': 'Missing territory parameter'}, status=400)
            if not scenario_id:
                return Response({'error': 'Missing scenario_id parameter'}, status=400)
            if not loc_cd:
                return Response({'error': 'No store selected'}, status=400)

            
            de_table = f"de_{concept.lower()}_{territory.lower()}_preopt"
            app_table = f"app_{concept.lower()}_preopt"
            cluster_table = f"app_{concept.lower()}_str_cluster"

            
            if performance_metric == "GMV":
                outlier_status_col = "gmv_outlier_status"
                suggested_total_lm_col = "gmv_suggested_total_lm"
                per_day_col = "gmv_per_day"
            else:
                outlier_status_col = "rev_outlier_status"
                suggested_total_lm_col = "rev_suggested_total_lm"
                per_day_col = "rev_per_day"

            selected_loc_cds = [str(l).strip() for l in loc_cd if l]
            # selected_clusters = [str(c).strip() for c in cluster if c]
            # selected_groups = [str(g).strip() for g in group if g]
            # selected_departments = [str(d).strip() for d in department if d]
            # selected_classes = [str(c).strip() for c in class_field if c]
            # selected_sub_classes = [str(s).strip() for s in sub_class if s]
            # selected_outlier_status = [str(o).strip() for o in outlier_status if o]

            where_clauses = ["app.scenario_id = %s"]
            params = [scenario_id]

            # if selected_loc_cds:
            #     placeholders = ','.join(['%s'] * len(selected_loc_cds))
            #     where_clauses.append(f"de.loc_cd IN ({placeholders})")
            #     params.extend(selected_loc_cds)

            # if selected_clusters:
            #     placeholders = ','.join(['%s'] * len(selected_clusters))
            #     where_clauses.append(f"cluster.new_cluster_num IN ({placeholders})")
            #     params.extend(selected_clusters)

            # if selected_groups:
            #     placeholders = ','.join(['%s'] * len(selected_groups))
            #     where_clauses.append(f"de.grp_nm IN ({placeholders})")
            #     params.extend(selected_groups)

            # if selected_departments:
            #     placeholders = ','.join(['%s'] * len(selected_departments))
            #     where_clauses.append(f"de.dpt_nm IN ({placeholders})")
            #     params.extend(selected_departments)

            # if selected_classes:
            #     placeholders = ','.join(['%s'] * len(selected_classes))
            #     where_clauses.append(f"de.clss_nm IN ({placeholders})")
            #     params.extend(selected_classes)

            # if selected_sub_classes:
            #     placeholders = ','.join(['%s'] * len(selected_sub_classes))
            #     where_clauses.append(f"de.sub_clss_nm IN ({placeholders})")
            #     params.extend(selected_sub_classes)

            # if selected_outlier_status:
            #     placeholders = ','.join(['%s'] * len(selected_outlier_status))
            #     where_clauses.append(f"app.{outlier_status_col} IN ({placeholders})")
            #     params.extend(selected_outlier_status)

            # Combine WHERE clauses
            where_sql = " AND ".join(where_clauses)

            data_sql = f'''
                SELECT
                    de.grp_nm,
                    de.dpt_nm,
                    de.clss_nm,
                    de.sub_clss_nm,
                    de.loc_nm,
                    de.month,
                    app.loc_cd,
                    de.total_lm,
                    de.lm_contribution_in_store,
                    de.{per_day_col} AS per_day,
                    app.outlier_status_final,
                    app.{outlier_status_col} AS outlier_status,
                    app.{suggested_total_lm_col} AS suggested_total_lm,
                    cluster.CLUSTER_NUM AS cluster_num,
                    cluster.new_cluster_num AS new_cluster_num
                FROM {de_table} de
                LEFT JOIN {app_table} app
                    ON de.grp_nm = app.grp_nm
                    AND de.dpt_nm = app.dpt_nm
                    AND de.clss_nm = app.clss_nm
                    AND de.sub_clss_nm = app.sub_clss_nm
                    AND de.loc_cd = app.loc_cd
                    AND de.month = app.month
                LEFT JOIN {cluster_table} cluster
                    ON de.loc_cd = cluster.loc_cd
                    AND cluster.scenario_id = {scenario_id}
                WHERE {where_sql}
            '''


            with connection.cursor() as cursor:
                cursor.execute(data_sql, params)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()

            data = [dict(zip(columns, row)) for row in rows]

            return Response({
                'data': data,
                'selected_loc_cd': selected_loc_cds
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': str(e),
                'message': 'An error occurred while fetching outlier data'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    

class ScenarioList(APIView):
    def post(self, request):
        try:
            # Get pagination params
            page = int(request.data.get('page', 1))
            page_size = int(request.data.get('page_size', 10))
            search = request.data.get('search', '').strip()
            status_check = request.data.get('status', '')
            offset = (page - 1) * page_size

            base_query = "FROM scenario_metad sm JOIN scenario_status ss on sm.id = ss.scenario_id LEFT JOIN users u ON u.id = sm.user_id where 1=1"
            params = []
            base_query += " AND sm.name IS NOT NULL AND sm.name != ''"
            base_query += " AND ss.STATUS = %s"
            params.append(f"{status_check}")

            # Add search filter if provided
            if search:
                base_query += " AND sm.name LIKE %s"
                params.append(f"%{search}%")

            # Total count query
            count_query = f"SELECT COUNT(*) {base_query}"

            # Data query with LIMIT/OFFSET
            data_query = f"""
                SELECT sm.id,sm.name,sm.eval_type,sm.event_name,sm.eval_start,sm.eval_end,ss.STATUS,sm.progress_page,u.first_name,u.last_name
                {base_query}
                ORDER BY id DESC
                LIMIT %s OFFSET %s
            """

            # Execute queries
            with connection.cursor() as cursor:
                cursor.execute(count_query, params)
                total_count = cursor.fetchone()[0]

                cursor.execute(data_query, params + [page_size, offset])
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()

            # Convert to list of dict
            data = [dict(zip(columns, row)) for row in rows]

            for item in data:
                item['first_name'] = item.get('first_name')
                item['last_name'] = item.get('last_name')

            return Response({
                "count": total_count,
                "total_pages":math.ceil(total_count / page_size),
                "page": page,
                "page_size": page_size,
                "results": data
            }, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

class testAndControlStore(APIView):

    def post(self, request):
        # Step 1: Validate input
        input_serializer = testAndControlStoreSerializer(data=request.data)
        if not input_serializer.is_valid():
            return Response(input_serializer.errors, status=400)

        validated = input_serializer.validated_data
        concept = validated["concept"]
        territory = validated["territory"]
        scenario_id = request.data['scenario_id']
        # Step 2: Resolve model from map
        table_name = f"de_{concept.lower()}_{territory.lower()}_cluster"
        ClusterModel = CLUSTER_MODEL_MAP.get(table_name)
        if not ClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        app_cluster_table = f"app_{concept.lower()}_str_cluster"
        AppClusterModel = STR_CLUSTER_MODEL_MAP.get(app_cluster_table)
        if not AppClusterModel:
            return Response({"error": f"Unsupported cluster table: {table_name}"}, status=400)

        # Step 3: Fetch data
        try:
            filtered_data = ClusterModel.objects.filter()
        except Exception as e:
            return Response({"error": f"Database error: {str(e)}"}, status=500)
        
        # Define matching subquery
        matching_locs = AppClusterModel.objects.filter(
            scenario_id=scenario_id,
            loc_cd=OuterRef('loc_cd')
        )
        
        annotated_data = filtered_data.annotate(
            new_cluster_num=Subquery(
                AppClusterModel.objects.filter(scenario_id=scenario_id,
                    loc_cd=OuterRef('loc_cd')  # match this to actual field
                ).values('new_cluster_num')[:1]
            )
        ).filter(
    Exists(matching_locs)
)

        return Response(list(annotated_data.values()))

    
class RunOutliers(APIView):
    def post(self, request):
        try:
                    # Validate request data
            serializer = OutlierDetectionRequestSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"error": "Invalid request data", "details": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            validated_data = serializer.validated_data
            scenario_id = validated_data['scenario_id']
            concept = validated_data['concept']
            territory = validated_data['territory']
            metric = validated_data['performance_metric'].upper()

            # Build table names
            app_table = f"app_{concept.lower()}_str_cluster"
            de_table = f"de_{concept.lower()}_{territory.lower()}_preopt"
            metric_per_day_col = f"{metric_map.get(metric)}_per_day".upper()

            # Build SQL join
            sql = f'''
                SELECT
                    app.new_cluster_num AS CLUSTER_NUM,
                    de.loc_cd AS LOC_CD,
                    de.month AS MONTH,
                    de.grp_nm AS GRP_NM,
                    de.dpt_nm AS DPT_NM,
                    de.clss_nm AS CLSS_NM,
                    de.sub_clss_nm AS SUB_CLSS_NM,
                    de.total_lm AS TOTAL_LM,
                    de.lm_contribution_in_store AS LM_CONTRIBUTION_IN_STORE,
                    de.{metric_per_day_col} as {metric_per_day_col}
                FROM {app_table} app
                LEFT JOIN {de_table} de
                    ON app.loc_cd = de.loc_cd
                WHERE app.scenario_id = %s
            '''

            with connection.cursor() as cursor:
                cursor.execute(sql, [scenario_id])
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()

            df = pd.DataFrame(rows, columns=columns)
            
            # Process outliers
            service = OutlierDetectionService()
            result = service.process_outliers(df, metric_map.get(metric))
            
            if result['success']:
                # Update your tables with the processed data
                processed_df = result['data']
                app_preopt_table_name = f"app_{concept.lower()}_preopt"
                AppPreoptModel = APP_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
                update_count = 0
                metric_lower = metric.lower()
                # Choose columns to update based on metric
                if metric_lower == "revenue":
                    outlier_status_col = "rev_outlier_status"
                    suggested_total_lm_col = "rev_suggested_total_lm"
                else:
                    outlier_status_col = "gmv_outlier_status"
                    suggested_total_lm_col = "gmv_suggested_total_lm"
                # Bulk update each row in processed_df (as DataFrame)
                processed_records = processed_df.astype(object).where(pd.notnull(processed_df), None).to_dict('records')

                # Optimize bulk update by batching and using transaction.atomic
                batch_size = 200
                total_updated = 0
                with transaction.atomic():
                    for i in range(0, len(processed_records), batch_size):
                        batch = processed_records[i:i+batch_size]
                        for row in batch:
                            filters = dict(
                                scenario_id=scenario_id,
                                grp_nm=row.get("GRP_NM"),
                                dpt_nm=row.get("DPT_NM"),
                                clss_nm=row.get("CLSS_NM"),
                                sub_clss_nm=row.get("SUB_CLSS_NM"),
                                loc_cd=row.get("LOC_CD"),
                                month=row.get("MONTH")
                            )
                            update_fields = {
                                outlier_status_col: row.get(outlier_status_col.upper()),
                                suggested_total_lm_col: row.get(suggested_total_lm_col.upper())
                            }
                            # ORM does not support true bulk update, so update per row in batch
                            updated = AppPreoptModel.objects.filter(**filters).update(**update_fields)
                            total_updated += updated
                update_count = total_updated

                return JsonResponse({
                    'status': 'success',
                    'message': result['message'],
                    'stats': result['stats'],
                    'updated_rows': update_count
                })
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': result['message']
                }, status=400)
                
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
            
class insertTestControlStr(APIView):
    def post(self, request):
        import time
        start_time = time.time()
        
        # Validate request data
        serializer = InsertTestControlStrRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        validated_data = serializer.validated_data
        scenario_id = validated_data['scenario_id']
        store_codes = validated_data['store_codes']  # Already validated as dict
        
        try:
            with transaction.atomic():
                # Check if scenario exists
                try:
                    scenario = ScenarioMetad.objects.get(id=scenario_id)
                except ScenarioMetad.DoesNotExist:
                    return Response(
                        {"error": f"Scenario with id {scenario_id} not found"},
                        status=status.HTTP_404_NOT_FOUND
                    )
                
                # Update the scenario's loc_cd field with all store codes
                current_time = timezone.now()
                scenario.loc_cd = store_codes
                scenario.updated_at = current_time
                scenario.save()
                
                execution_time = time.time() - start_time
                
                response_data = {
                    "message": "Test control store data inserted successfully",
                    "scenario_id": scenario_id,
                    "store_mapping": store_codes,
                    "execution_time": round(execution_time, 2)
                }
                
                response_serializer = InsertTestControlStrResponseSerializer(data=response_data)
                if response_serializer.is_valid():
                    return Response(response_serializer.data, status=status.HTTP_200_OK)
                else:
                    return Response(response_data, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response(
                {"error": "An error occurred while inserting test control store data", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def patch(self, request):
        try:
            scenario_id = request.data.get('scenario_id')
            concept = request.data.get('concept')
            territory = request.data.get('territory')
            store_codes = request.data.get('store_codes')

            if not all([scenario_id, concept, territory, store_codes]):
                return Response({
                    "error": "Missing required fields",
                    "message": "scenario_id, concept, territory, and store_codes are required"
                }, status=500)

            store_codes_remove = json.loads(store_codes)
            scenario = ScenarioMetad.objects.get(id=scenario_id)
            store_codes_current = json.loads(scenario.loc_cd)

            remove_stores = {item['testStore'] for item in store_codes_remove}
            
            updated_loc_cd = []
            for item in store_codes_current:
                if item.get('testStore') not in remove_stores:
                    updated_loc_cd.append(item)


            scenario.loc_cd = json.dumps(updated_loc_cd)
            scenario.save()

            return Response(updated_loc_cd, status=200)

        except Exception as e:
            return Response({
                "error": "Internal server error",
                "message": str(e)
            }, status=500)

        
class getDataSummary(APIView):
    def post(self, request):
        # Validate request data
        print(request.data)
        serializer = GraphDataRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        validated_data = serializer.validated_data
        concept = validated_data['concept']
        group_filter = validated_data.get('group', '')
        department_filter = validated_data.get('department', [])
        class_filter = validated_data.get('class', [])
        sub_class_filter = validated_data.get('sub_class', [])
        from_month = validated_data['from_month']
        to_month = validated_data['to_month']
        territory = validated_data['territory_name']
        metric = validated_data['metric']
        loc_cd = validated_data.get('loc_cd')
        try:
            # Get model reference
            de_preopt_table_name = f"de_{concept.lower()}_{territory.lower()}_preopt"
            DePreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(de_preopt_table_name)

            app_preopt_table_name = f"app_{concept.lower()}_preopt"
            AppPreoptModel = APP_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
            
            if not DePreoptModel:
                return Response(
                    {"error": f"Model not found for concept: {concept}"},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # Convert month format for filtering (MM/YYYY to YYYY-MM or your DB format)
            from_month_db = self.convert_month_format(from_month)
            to_month_db = self.convert_month_format(to_month)
            

            query, params = self.get_total_lm(
                DePreoptModel, de_preopt_table_name,
                app_preopt_table_name, from_month_db,
                to_month_db,
                group_filter, department_filter,
                class_filter, sub_class_filter,loc_cd
                
            )

            with connection.cursor() as cursor:
                cursor.execute(query, params)  # <-- pass both query and parameters
                columns = [col[0] for col in cursor.description]
                aggregated_data = [dict(zip(columns, row)) for row in cursor.fetchall()]

            

            data_points = []
            for item in aggregated_data:
                try:
                    total_lm = float(item.get('total_lm', 0) or 0)
                    
                    avg_gmv_per_lm = float(item.get('avg_gmv_per_lm', 0) or 0)

                    productivity = float(item.get('productivity',0) or 0 )

                    net_sls_amt = float(item.get('net_sls_amt', 0) or 0)
                    rev_per_day = float(item.get('rev_per_day', 0) or 0)
                    gmv = float(item.get('gmv', 0) or 0)
                    gmv_per_day = float(item.get('gmv_per_day', 0) or 0)

                    # Calculate metrics based on aggregated values
                    try:
                        if metric == 'revenue_per_lm':
                            metric_value = net_sls_amt / total_lm if total_lm != 0 else 0
                        elif metric == 'spd':
                            metric_value = rev_per_day
                        elif metric == 'gmv_per_lm':
                            metric_value = gmv / total_lm if total_lm != 0 else 0
                        elif metric == 'gmv_per_day':
                            metric_value = gmv_per_day
                        else:
                            metric_value = 0
                    except (ValueError, ZeroDivisionError, TypeError) as e:
                        print(f"Error calculating metric: {e}")
                        metric_value = 0


                    data_point = {
                        'month': item.get('month'),
                        'total_lm': total_lm,
                        'productivity': productivity,
                        'gmv_per_lm': avg_gmv_per_lm,
                        'metric': metric,
                        'metric_value': round(metric_value, 2),
                    }


                    data_points.append(data_point)
                except Exception as e:
                    print(f"Error processing row: {item} -> {e}")
            
            response_data = {
                "message": "Graph data retrieved successfully",
                "data_points": data_points,
                "total_records": len(data_points),
                "filters_applied": {
                    "group": group_filter,
                    "department": department_filter,
                    "class": class_filter,
                    "sub_class": sub_class_filter,
                    "from_month": from_month,
                    "to_month": to_month,
                    "stores": loc_cd  # Include list of stores in response
                }
            }
            
            response_serializer = GraphDataResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                return Response(response_serializer.data, status=status.HTTP_200_OK)
            else:
                return Response(response_data, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response(
                {"error": "An error occurred while retrieving graph data", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def convert_month_format(self, month_str):
        """Convert MM/YYYY to your database month format"""
        # Adjust this based on your actual database month format
        # If month field stores as 'YYYY-MM', 'MM/YYYY', or just 'MM' etc.
        year, month = month_str.split('-')
        return f"{year}{month.zfill(2)}"  # Assuming YYYY-MM format in DB
        # Or return month_str if it matches your DB format exactly

    def get_total_lm(self, DePreoptModel, de_preopt_table, app_preopt_table, from_month, to_month, group_filter, department_filter,
                 class_filter, sub_class_filter, loc_cd):
        
        where_clauses = []
        params = []

        # Required filters
        where_clauses.append("d.month BETWEEN %s AND %s")
        params.extend([from_month, to_month])

        if loc_cd:
            clause, values = self.build_in_clause("d.loc_cd", loc_cd)
            where_clauses.append(clause)
            params.extend(values)

        if group_filter:
            clause, values = self.build_in_clause("d.grp_nm", group_filter)
            where_clauses.append(clause)
            params.extend(values)

        if department_filter:
            clause, values = self.build_in_clause("d.dpt_nm", department_filter)
            where_clauses.append(clause)
            params.extend(values)

        if class_filter:
            clause, values = self.build_in_clause("d.clss_nm", class_filter)
            where_clauses.append(clause)
            params.extend(values)

        if sub_class_filter:
            clause, values = self.build_in_clause("d.sub_clss_nm", sub_class_filter)
            where_clauses.append(clause)
            params.extend(values)

        where_sql = " AND ".join(where_clauses) or "1=1"

        # SQL query remains the same since it's already doing aggregation
        query = f"""
            SELECT
                d.month,
                SUM(d.REV_PER_DAY) AS rev_per_day,
                SUM(d.net_sls_amt) AS net_sls_amt,
                SUM(d.gmv_per_day) AS gmv_per_day,
                SUM(d.TOTAL_LM) AS total_lm,
                SUM(d.gmv) AS gmv,
                AVG(d.gmv_per_lm) AS gmv_per_lm,
                SUM(d.gmv)/NULLIF(sum(d.total_lm), 0) as productivity
            FROM
                {de_preopt_table} d
            WHERE
                {where_sql}
            GROUP BY
                 d.month
            ORDER BY
                d.month
        """
        return query, params  # You should execute this with cursor.execute(query, params)
    
    def build_in_clause(self, field, values):
        placeholders = ', '.join(['%s'] * len(values))
        clause = f"{field} IN ({placeholders})"
        return clause, values


        
class getMetricGraph(APIView):
    def post(self, request):
        try:
            # Get request data
            data = request.data
            concept = data.get('concept')
            loc_cd = data.get('loc_cd')
            metric_field = data.get('metric')
            from_month = data.get('from_month', '')
            to_month = data.get('to_month', '')
            territory_name = data.get('territory_name', '')
            # Validate required fields
            if not all([concept, metric_field]):
                return Response({
                    "success": False,
                    "message": "concept, and metric are required"
                }, status=400)
            
            
            # Get model reference
            app_preopt_table_name = f"de_{concept.lower()}_{territory_name.lower()}_preopt"
            DePreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
            
            if not DePreoptModel:
                return Response({
                    "success": False,
                    "message": f"Model not found for concept: {concept}"
                }, status=400)
            
            # Handle loc_cd as list or single value
            if loc_cd:
                if isinstance(loc_cd, list):
                    loc_cd_filter = loc_cd
                else:
                    loc_cd_filter = [loc_cd]
            else:
                loc_cd_filter = None
            
            # Get the latest month for the specific scenario and stores

            from_date = datetime.strptime(from_month, '%Y-%m')
            to_date = datetime.strptime(to_month, '%Y-%m')

            # Calculate the difference in months
            month_diff = ((to_date.year - from_date.year) * 12 + (to_date.month - from_date.month)+1)
            from_month = self.convert_month_format(from_month)
            to_month = self.convert_month_format(to_month)
            
            month_query = DePreoptModel.objects.filter(
                month__gte=from_month,
                month__lte=to_month
            )

            if loc_cd_filter:
                month_query = month_query.filter(loc_cd__in=loc_cd_filter)
            

            total_days = month_diff * 30
            
            if not month_query:
                return Response({
                    "success": False,
                    "message": f"No data found for stores "
                }, status=404)
            
            # Get subclass data for the latest month
            annotation_dict = self.metricsField(metric_field, total_days)
            
            filters = {
                'month__gte': from_month,
                'month__lte': to_month,
            }

            if loc_cd_filter:
                filters['loc_cd__in'] = loc_cd_filter
            

            subclass_data = (DePreoptModel.objects
                .filter(**filters)
                .values('sub_clss_nm')
                .annotate(**annotation_dict)
                .order_by('-metric_total')
            )
            
            if not subclass_data.exists():
                return Response({
                    "success": False,
                    "message": f"No subclass data found for stores {loc_cd_filter} in month {month_query}"
                }, status=404)
            
            # Convert to list and get top 5 and bottom 5
            subclass_list = list(subclass_data)
            total_subclasses = len(subclass_list)
            
            # Top 5 (highest values)
            top_5_data = []
            for i, item in enumerate(subclass_list[:5], 1):
                top_5_data.append({
                    'sub_class_name': item['sub_clss_nm'],
                    'metric_value': float(item['metric_total'] or 0),
                    'rank': i,
                    'category': 'top'
                })
            
            # Bottom 5 (lowest values)
            bottom_5_data = []
            if total_subclasses > 5:
                bottom_5_list = subclass_list[-5:]
                for i, item in enumerate(reversed(bottom_5_list), 1):
                    bottom_5_data.append({
                        'sub_class_name': item['sub_clss_nm'],
                        'metric_value': float(item['metric_total'] or 0),
                        'rank': total_subclasses - 5 + (6 - i),  # Correct rank calculation
                        'category': 'bottom'
                    })
            
            # Get store names for reference
            store_info = DePreoptModel.objects.filter(
                month__gte=from_month,
                month__lte=to_month
            ).values('loc_cd', 'loc_nm').distinct()
            
            stores = [{"code": store['loc_cd'], "name": store['loc_nm']} for store in store_info]
            
            return Response({
                "success": True,
                "message": "Metric graph data retrieved successfully",
                "stores": stores,
                "metric": metric_field,
                "top_5": top_5_data,
                "bottom_5": bottom_5_data,
                "total_subclasses": total_subclasses,
            })
                
        except Exception as e:
            return Response({
                "success": False,
                "message": "An error occurred while retrieving metric graph data", 
                "details": str(e)
            }, status=500)
    
    def convert_month_format(self, month_str):
        """Convert MM/YYYY to your database month format"""
        # Adjust this based on your actual database month format
        # If month field stores as 'YYYY-MM', 'MM/YYYY', or just 'MM' etc.
        year, month = month_str.split('-')
        return f"{year}{month.zfill(2)}"  # Assuming YYYY-MM format in DB
        # Or return month_str if it matches your DB format exactly
    
    def metricsField(self, metric_field, total_days):

        if metric_field == 'revenue_per_lm':
            annotation_dict = {
                'metric_total': ExpressionWrapper(
                    Sum('net_sls_amt') / NullIf(Avg('rev_suggested_total_lm'), 0),
                    output_field=FloatField()
                )
            }
            return annotation_dict
        
        elif metric_field == 'spd':
            annotation_dict = {
                'metric_total': ExpressionWrapper(
                    Sum('net_sls_amt') / total_days,
                    output_field=FloatField()
                )
            }
            return annotation_dict

        elif metric_field == 'gmv_per_lm':
            annotation_dict = {
                'metric_total': ExpressionWrapper(
                    Sum('gmv') / NullIf(Avg('gmv_suggested_total_lm'), 0),
                    output_field=FloatField()
                )
            }
            return annotation_dict

        elif metric_field == 'gmv_per_day':
            annotation_dict = {'metric_total': Sum('gmv')/ total_days}
            return annotation_dict

        else:
            return Response({
                "error": "Invalid metric",
                "message": f"Unsupported metric: {metric_field}"
            }, status=400)

class getAllGDCSdata(APIView):
    """
    Optimized API to get distinct GDCS data with specific fields only
    """
    
    def post(self, request):
        try:
            # Validate request data
            request_serializer = GDCSDataRequestSerializer(data=request.data)
            if not request_serializer.is_valid():
                return Response({
                    'success': False,
                    'message': 'Invalid request data',
                    'errors': request_serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)
            
            validated_data = request_serializer.validated_data
            concept = validated_data['concept']
            territory = validated_data.get('territory_name')
            
            # Get the appropriate model based on concept
            de_preopt_table_name = f"de_{concept.lower()}_{territory.lower()}_preopt"
            DePreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(de_preopt_table_name)
            
            if not DePreoptModel:
                return Response({
                    'success': False,
                    'message': f'No model found for concept: {concept}',
                    'data': []
                }, status=status.HTTP_404_NOT_FOUND)
            
            query = DePreoptModel.objects.all()

            month_range = query.aggregate(
                start_month=Min('month'),
                end_month=Max('month')
            )

            # if loc_cd:
            #     query = query.filter(loc_cd__in=loc_cd)
            
            loc_cd_month_ranges = query.values('loc_cd').annotate(
                start_month=Min('month'),
                end_month=Max('month')
            ).order_by('loc_cd')
            

            gdcs_data = query.values(
                'loc_cd', 'loc_nm', 'rgn_nm',
                'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm'
            ).distinct().order_by('loc_cd', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm')


            return Response({
                'success': True,
                'message': 'GDCS data retrieved successfully',
                'gdcs_data': gdcs_data,
                'total_records': len(gdcs_data),
                'start_date': month_range['start_month'],
                'end_date': month_range['end_month'],
                'month_ranges_by_loc_cd': list(loc_cd_month_ranges)
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': 'Internal server error occurred',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class getOptimizerDetails(APIView):
    #TODO: handle case when user uploads multiple files for same type in a scenario
    def post(self, request):
        try:
            # Get scenario_id from request body
            data = json.loads(request.body)
            scenario_id = data.get('scenario_id')
            
            if not scenario_id:
                return Response({
                    'success': False,
                    'message': 'scenario_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get scenario details
            scenario = ScenarioMetad.objects.get(id=scenario_id)
            
            # Prepare response data
            optimizer_details = {
                'id': scenario.id,
                'name': scenario.name,
                'user_id': scenario.user_id,
                'season_type': scenario.season_type,
                'eval_type': scenario.eval_type,
                'event_name': scenario.event_name,
                'eval_start': scenario.eval_start,
                'eval_end': scenario.eval_end,
                'ref_start': scenario.ref_start,
                'ref_end': scenario.ref_end,
                'CNCPT_NM': scenario.CNCPT_NM,
                'TERRITORY_NM': scenario.TERRITORY_NM,
                'metric': scenario.metric,
                'sqft_file_id': scenario.sqft_file_id,
                'mdq_file_id': scenario.mdq_file_id,
                'cover_file_id': scenario.cover_file_id,
                'exclusion_file_id': scenario.exclusion_file_id,
                'sqft_filename': scenario.sqft_filename,
                'mdq_filename': scenario.mdq_filename,
                'cover_filename': scenario.cover_filename,
                'exclusion_filename': scenario.exclusion_filename,
                'loc_cd': scenario.loc_cd,
                'created_by': scenario.created_by,
                'updated_by': scenario.updated_by,
                'created_at': scenario.created_at,
                'updated_at': scenario.updated_at,
                'weights': scenario.weights,
                'progress_page': scenario.progress_page,
                'current_page': scenario.current_page,
            }
            
            return Response({
                'success': True,
                'data': optimizer_details
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'success': False,
                'message': f'An error occurred: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class UpdatePerformanceActionView(APIView):
    def post(self, request):
        try:
            department = request.data.get('department_name')
            group_name = request.data.get('group_name')
            class_name = request.data.get('class_name')
            subclass = request.data.get('subclass_name')
            new_action = request.data.get('new_action')
            loc_cd = request.data.get('location_code')
            scenario_id = request.data.get('scenario_id')
            concept = request.data.get('concept')
            print("request", request.data)
            if not all([department, class_name, subclass, new_action]):
                return Response({'error': 'Missing required parameters.'}, status=status.HTTP_400_BAD_REQUEST)

            app_table_name = f"ds_{concept.lower()}_performance_ref"
            dsPerformanceRef = PERFORMANCE_MODEL_MAP.get(app_table_name)
            updated_count = dsPerformanceRef.objects.filter(
                GRP_NM=group_name,
                DPT_NM=department,
                CLSS_NM=class_name,
                SUB_CLSS_NM=subclass,
                LOC_CD=loc_cd,
                scenario_id=scenario_id
            ).update(new_action=new_action)
            return Response({
                'message': f'{updated_count} rows updated successfully.',
                'updated_action': new_action
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def call_fastapi_endpoint(url: str, payload: dict = None):
    try:
        response = requests.post(url, json=payload or {})
        if response.status_code == 200:
            return response.json()
        else:
            return {
                "error": f"FastAPI error: {response.status_code}",
                "detail": response.text
            }
    except Exception as e:
        return {"error": str(e)}

class UpdateScenarioWeightsView(APIView):
    def post(self, request):
        scenario_id = request.data.get('scenario_id')
        weights = request.data.get('weights')

        if not scenario_id or weights is None:
            return Response({"error": "scenario_id and weights are required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            scenario = ScenarioMetad.objects.get(id=scenario_id)
        except ScenarioMetad.DoesNotExist:
            return Response({"error": "Scenario not found"}, status=status.HTTP_404_NOT_FOUND)

        scenario.weights = weights
        scenario.save()

        # # Call FastAPI endpoints
        # base_url = "https://lmdllcloudapp-dev.landmarkgroup.com/space_openai/fastapi"
        # run_scenario_url = f"{base_url}/run_scenario"
        # data_preparation_url = f"{base_url}/run/datapreparation"
        # saturation_url = f"{base_url}/run/saturation"
        # print(run_scenario_url,data_preparation_url,saturation_url)

        # payload = {
        #     "SCENARIO_ID": str(scenario.id),
        # }
        # scenarioPayload = {
        #     "scenario_id": scenario.id,
        # }
        # run_scenario_result = call_fastapi_endpoint(run_scenario_url, scenarioPayload)
        # if (
        #     isinstance(run_scenario_result, dict)
        #     and "error" in run_scenario_result
        # ):
        #     return Response({
        #         'error': f'error in run scenario:'
        #     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        # print("Starting Data Preparation module...", payload)
        # data_preparation_result = call_fastapi_endpoint(data_preparation_url, payload)
        #  # Check for error in datapreparation result
        # if (
        #     isinstance(data_preparation_result, dict)
        #     and "error" in data_preparation_result
        # ):
        #     return Response({
        #         'error': f'error in datapreparation:'
        #     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # # Proceed only if no error
        # saturation_result = call_fastapi_endpoint(saturation_url, payload)

        # if (
        #     isinstance(saturation_result, dict)
        #     and "error" in saturation_result
        # ):
        #     return Response({
        #         'error': f'error in saturation:'
        #     }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


        return Response({"message": "Weights updated successfully", "weights": scenario.weights})



class FilteredPerformanceDataView(APIView):
    def post(self, request):
        try:
            filters = request.data
            print("request value is", filters)

            group = filters.get("group", [])
            department = filters.get("department")
            class_name = filters.get("class", [])
            subclass = filters.get("subclass_names", [])
            recommendation = filters.get("recommendation",[])
            stores = filters.get("store_ids", [])
            scenario_id = filters.get("scenario_id")
            concept = filters.get("concept")
            territory = filters.get("territory")
            page = int(filters.get("page", 1))  # Default page 1
            page_size = int(filters.get("page_size", 50))  # Default page size 50
            offset = (page - 1) * page_size
            final_grade = filters.get("final_grade",[])
            sort_by = filters.get("sortBy", "")
            sort_order = filters.get("sortOrder", "ASC")  

            valid_sort_columns = {
                "d_units": "perf.d_units",
                "UNITS_PER_INV": "perf.UNITS_PER_INV",
                "CUST_PEN": "perf.CUST_PEN",
                "COVER": "perf.COVER",
                "MARGIN_PERC": "perf.MARGIN_PERC",
                "ASP": "perf.ASP",
                "Productivity": "perf.Productivity",
                "Performance": "perf.Performance",
            }

            # Default sort
            sort_column = valid_sort_columns.get(sort_by, "perf.SUB_CLSS_NM")

            where_clauses = []
            params = []
            if group:
                clause, values = self.build_in_clause("GRP_NM", group)
                where_clauses.append(clause)
                params.extend(values)

            if department:
                where_clauses.append("DPT_NM = %s")
                params.append(department)

            if class_name:
                clause, values = self.build_in_clause("CLSS_NM", class_name)
                where_clauses.append(clause)
                params.extend(values)

            if subclass:
                clause, values = self.build_in_clause("SUB_CLSS_NM", subclass)
                where_clauses.append(clause)
                params.extend(values)
            if recommendation:
                clause, values = self.build_in_clause("action_p", recommendation)
                where_clauses.append(clause)
                params.extend(values)

            if stores:
                clause, values = self.build_in_clause("perf.LOC_CD", stores)
                where_clauses.append(clause)
                params.extend(values)
            
            if scenario_id is not None:
                where_clauses.append("SCENARIO_ID = %s")
                params.append(scenario_id)

            if final_grade:
                final_grade_sql = "CONCAT(perf_bucket_p, lm_bucket_p, cover_bucket_p)"
                clause, values = self.build_in_clause(final_grade_sql, final_grade)
                where_clauses.append(clause)
                params.extend(values)


            where_sql = " AND ".join(where_clauses) or "1=1"  # fallback

            count_query = f"""
                SELECT COUNT(*)
                FROM ds_{concept}_performance_ref perf
                LEFT JOIN de_{concept}_{territory}_cluster clus
                    ON perf.LOC_CD = clus.loc_cd
                WHERE {where_sql}
            """
            with connection.cursor() as cursor:
                cursor.execute(count_query, params)  # remove last two params (page_size, offset)
                total_count = cursor.fetchone()[0]

            query = f"""
                    SELECT
                        perf.GRP_NM,
                        perf.DPT_NM,
                        perf.CLSS_NM,
                        perf.SUB_CLSS_NM,
                        perf.d_units,
                        perf.LOC_CD,
                        clus.loc_nm,
                        perf.UNITS_PER_INV,
                        perf.CUST_PEN,
                        perf.COVER,
                        perf.MARGIN_PERC,
                        perf.ASP,
                        perf.lm_bucket_p,
                        perf.cover_bucket_p,
                        perf.action_p,
                        perf.perf_bucket_p,
                        perf.Linear_meter,
                        perf.new_action,
                        perf.Productivity,
                        ROUND(perf.Performance, 2) AS Performance,
                        CONCAT(perf_bucket_p, lm_bucket_p, cover_bucket_p) AS final_grade
                    FROM ds_{concept}_performance_ref perf
                    LEFT JOIN de_{concept}_{territory}_cluster clus
                        ON perf.LOC_CD = clus.loc_cd
                    WHERE {where_sql}
                    ORDER BY {sort_column} {sort_order}
                    LIMIT %s OFFSET %s
                """
            
            params.extend([page_size, offset])

            with connection.cursor() as cursor:
                cursor.execute(query, params)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                data = [dict(zip(columns, row)) for row in rows]

            return Response({
                "data": data,
                "total_count": total_count
            }, status=status.HTTP_200_OK)


        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response(
                {"status": "error", "message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    

    def build_in_clause(self, field, values):
        placeholders = ', '.join(['%s'] * len(values))
        clause = f"{field} IN ({placeholders})"
        return clause, values


class HealthMetricsAPIView(APIView):
    """API view for data processing with pagination"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.service = HealthMetricsProcessing()
        self.summaryService = SummaryTotalCalculator()
        self.output_fields = [
            'loc_cd','loc_nm', 'grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm',
            'sqft', 'lm', 'option_count', 'soh', 'inv_cnt', 'rev', 'gmv',
            'ros', 'cover', 'rev_per_day', 'gmv_per_day', 'option_density',
            'stock_density', 'gmv_per_lm_per_day', 'rev_per_lm_per_day',
            'lm_rank', 'sqft_rank', 'rev_per_sqft_per_day_rank',
            'gmv_per_sqft_per_day_rank', 'rev_per_lm_per_day_rank',
            'gmv_per_lm_per_day_rank', 'fixture_density',
            'sqft_contribution_in_store', 'diff_lm_sqft_contrib',
            'lm_contribution_in_store', 'rev_per_sqft_per_day',
            'gmv_per_sqft_per_day','mnth_end_soh','gmroi','rev_per_lm','gmv_per_lm','rev_per_sqft','gmv_per_sqft', 'area_in_sqft'
        ]
    
    def filter_output_fields(self, data: list) -> list:
        """Filter data to include only specified output fields"""
        filtered_data = []
        for item in data:
            filtered_item = {
                field: item.get(field, None) for field in self.output_fields
                if field in item
            }
            filtered_data.append(filtered_item)
        return filtered_data
    
    def post(self, request):
        """Handle POST request with pagination"""
        try:
            # Extract request parameters
            data = request.data
            ref_period = data.get('ref_period', {})
            concept = data.get('concept')
            territory = data.get('territory','AE')
            filter_params = data.get('filter_params', {})
            scenario_id = data.get('scenario_id')
            
            # Get pagination params
            page = int(request.data.get('page', 1))
            page_size = int(request.data.get('page_size', 10))
            app_preopt_table_name = f"app_{concept.lower()}_preopt"
            AppPreoptModel = APP_PREOPTIMIZATION_MODEL_MAP.get(app_preopt_table_name)
            de_preopt_table = f"de_{concept.lower()}_{territory.lower()}_preopt"
            DePreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(de_preopt_table)
            
            # Validate required parameters
            if not ref_period or not ref_period.get('start') or not ref_period.get('end'):
                return Response({
                    'error': 'ref_period with start and end dates is required',
                }, status=status.HTTP_400_BAD_REQUEST)
            
            ref_period["start"] = datetime.strptime(filter_params["from_month[]"], "%Y-%m").strftime("%Y%m")
            ref_period["end"] = datetime.strptime(filter_params["to_month[]"], "%Y-%m").strftime("%Y%m")
            start = ref_period["start"]
            end = ref_period["end"]
            
            # Get base queryset (KEEP AS QUERYSET - don't paginate yet)
            base_queryset = DePreoptModel.objects.filter(
                month__gte=int(ref_period['start']),
                month__lte=int(ref_period['end'])
            )
            
            de_fields = [f.name for f in DePreoptModel._meta.fields]
            de_field_str = ', '.join([f'd.{field}' for field in de_fields])
            final_select = f"""
                {de_field_str}
            """
            params_filtered = [start, end]
            

            query_filtered = f"""
            SELECT DISTINCT
                {final_select}
            FROM
                {de_preopt_table} d
            WHERE
                d.month BETWEEN %s AND %s
            """

            # Also, since no join and no filters, query_all can be same but without WHERE clause
            query_all = f"""
            SELECT DISTINCT
                {final_select}
            FROM
                {de_preopt_table} d
            """

            with connection.cursor() as cursor:
                # print("query is", query)
                # print("Final SQL query:\n", query_filtered.replace('%s', '{}').format(*[f"'{p}'" if isinstance(p, str) else p for p in [start, end, scenario_id]]))
                cursor.execute(query_filtered, params_filtered)
                columns = [col[0] for col in cursor.description]
                raw_results = [
                    dict(zip(columns, row)) for row in cursor.fetchall()
                ]

                cursor.execute(query_all)
                columns = [col[0] for col in cursor.description]
                results_all = [dict(zip(columns, row)) for row in cursor.fetchall()]

            # import csv; [csv.DictWriter(open('query_results.csv', 'w', newline='', encoding='utf-8'), fieldnames=columns).writeheader(), csv.DictWriter(open('query_results.csv', 'a', newline='', encoding='utf-8'), fieldnames=columns).writerows(raw_results)]
            # print(raw_results[0])
            # Process data BEFORE pagination
            processed_data = self.service.process_data(
                base_queryset=raw_results,
                base_queryset_all=results_all,
                ref_period=ref_period,
                filter_params=filter_params,
            )

            if not processed_data:
                return Response({
                    'results': [],
                    'count': 0,
                    'next': None,
                    'previous': None,
                    'message': 'No data found for the given parameters',
                    'summary_totals': []
                }, status=status.HTTP_200_OK)

            # Filter to only include specified output fields
            filtered_data = self.filter_output_fields(processed_data)
            summary_totals = self.summaryService.calculate_summary_totals(filtered_data)

            # Apply pagination AFTER processing
            # paginator = Paginator(filtered_data, page_size)
            
            # if page > paginator.num_pages:
            #     return Response({
            #         'error': f'Page {page} does not exist. Total pages: {paginator.num_pages}',
            #     }, status=status.HTTP_400_BAD_REQUEST)
            
            # paginated_data = paginator.page(page)
            def normalize_loc(loc):
                return loc.replace(" - ", "-").replace("  ", " ").strip().lower()

            sorted_data = sorted(filtered_data,key=lambda x: (normalize_loc(x['loc_nm']), x['grp_nm'], x['dpt_nm'], x['clss_nm'], x['sub_clss_nm']))
            # Build response
            response_data = {
                'message': 'Data processed successfully',
                # 'count': paginator.count,
                # 'results': list(filtered_data),
                'results': sorted_data,
                'summary_totals': summary_totals,
                # 'total_pages': paginator.num_pages,
                # 'current_page': paginated_data.number,
                'page_size': page_size,
                # 'next': paginated_data.next_page_number() if paginated_data.has_next() else None,
                # 'previous': paginated_data.previous_page_number() if paginated_data.has_previous() else None,
            }

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({
                'error': f'error during processing health metrics: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class FileUploadAPIView(APIView):
    """
    API endpoint to handle file uploads, validation, and database insertion.
    """

    def post(self, request, *args, **kwargs):

        """
        Handles file upload, validation, and DB insert.

        Expects:
        - file (uploaded .csv or .xlsx)
        - file_type (must exist in FILE_SCHEMAS)

        Process:
        1. Validate file & type, check extension.
        2. Load into Pandas DataFrame, normalize columns.
        3. Validate required columns & rename for DB schema.
        4. Save metadata in FileUploads.
        5. Bulk insert rows into mapped model.

        Returns:
        - 201: Success with file metadata
        - 400: Validation error (e.g., missing columns, bad input)
        - 500: Unexpected processing error
        """

        file_obj = request.FILES.get("file")
        file_type = request.data.get("file_type").lower()
        scenario_id = request.data.get("scenario_id")
        

        ALLOWED_EXTENSIONS = ["xlsx", "csv"]

        if not file_obj or not file_type:
            return Response(
                {"status": "error", "message": "file and file_type are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if file_type not in FILE_SCHEMAS:
            return Response(
                {"status": "error", "message": f"Invalid file_type: {file_type}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        ext = os.path.splitext(file_obj.name)[1].lower().replace(".", "")
        if ext not in ALLOWED_EXTENSIONS:
            return Response(
                {
                    "status": "error",
                    "file_name": file_obj.name,
                    "message": f"Invalid file extension '{ext}'. Allowed: {ALLOWED_EXTENSIONS}",
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # ------------------------
            # Load Excel into DataFrame
            # ------------------------
            if ext == "csv":
                df = pd.read_csv(file_obj)
            else:  # xlsx
                df = pd.read_excel(file_obj)
            df.columns = df.columns.str.strip()    

            # ------------------------
            # Validate required columns
            # ------------------------
            required_cols = set(FILE_SCHEMAS[file_type].keys())
            missing_cols = required_cols - set(df.columns)
            if missing_cols:
                return Response(
                    {
                        "status": "error",
                        "file_name": file_obj.name,
                        "message": f"Missing required columns: {', '.join(missing_cols)}",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # ------------------------
            # Rename to match DB fields
            # ------------------------
            df = df.rename(columns=FILE_SCHEMAS[file_type])

            # ------------------------
            # Save metadata in FILE_UPLOADS
            # ------------------------
            file_upload = FileUploads.objects.create(
                file_name=file_obj.name,
                file_type=file_type,
                uploaded_at=timezone.now(),
            )

            # ------------------------
            # Bulk insert into respective table
            # ------------------------
            Model = FILE_MODELS[file_type]
            
            if scenario_id != 'undefined':
                existing = Model.objects.filter(scenario_id=int(scenario_id))
            

                if existing.exists():
                    existing.delete()
            records = df.to_dict(orient="records")
            bulk_objects = [Model(file_upload=file_upload, **row) for row in records]
            Model.objects.bulk_create(bulk_objects, batch_size=500)

            return Response(
                {
                    "status": "success",
                    "file_id": file_upload.id,
                    "file_name": file_upload.file_name,
                    "file_type": file_upload.file_type,
                },
                status=status.HTTP_201_CREATED,
            )

        except ValueError as ve:
            return Response(
                {
                    "status": "error",
                    "file_name": file_obj.name,
                    "message": str(ve),
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            return Response(
                {
                    "status": "error",
                    "file_name": file_obj.name,
                    "message": "Unexpected error while processing file",
                    "error": str(e),
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            
PAGE_TABLE_MAP = {
    "performance": {
        "table": "ds_{concept}_performance_ref",
        "filters": ["LOC_CD", "SUB_CLSS_NM"]
    },
    "optimization": {
        "table": "{concept}_optimization_summary",
        "filters": ["LOC_CD", "SUB_CLSS_NM", "LOC_NM", "GRP_NM", "DPT_NM", "CLSS_NM"]
    },
    "outlier": {
        "table": "ds_{concept}_outlier_ref",
        "filters": ["LOC_CD", "SUB_CLSS_NM", "OUTLIER_TYPE"]
    }
}


class DynamicFilterView(APIView):
    def post(self, request):
        try:
            scenario_id = request.data.get("scenario_id")
            concept = request.data.get("concept")
            page = request.data.get("page")
            territory = request.data.get("territory")

            if not scenario_id or not concept or not page:
                return Response(
                    {"status": "error", "message": "scenario_id, concept, and page are required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            page_config = PAGE_TABLE_MAP.get(page)
            if not page_config:
                return Response(
                    {"status": "error", "message": f"Unknown page type: {page}"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            table = page_config["table"].format(concept=concept)
            filters = page_config.get("filters", [])

            with connection.cursor() as cursor:
                # Build query to fetch all columns in filters for the given scenario
                cols = ", ".join([f"main.{f}" for f in filters])

                query = f"SELECT DISTINCT {cols}, lm.LOC_NM LOC_NM FROM {table} main LEFT JOIN loc_metad lm ON lm.LOC_CD = main.LOC_CD WHERE main.SCENARIO_ID = %s"
                cursor.execute(query, [scenario_id])
                rows = cursor.fetchall()
                col_names = [desc[0] for desc in cursor.description]

                result_list = [dict(zip(col_names, row)) for row in rows]

            return Response({"status": "success", "filters": result_list}, status=status.HTTP_200_OK)

        except Exception as e:
            import traceback
            traceback.print_exc()
            return Response(
                {"status": "error", "message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
            
class GetOptimizationFilters(APIView):
    def get(self, request):
        try:
            scenario_id = request.query_params.get('scenario_id')
            concept = request.query_params.get('concept')
            territory = request.query_params.get('territory')

            if not scenario_id or not concept or not territory:
                return Response({
                    "success": False,
                    "message": "Missing required parameters: scenario_id, concept, territory"
                }, status=400)
            
            preopt_table_name = f"de_{concept.lower()}_{territory.lower()}_preopt"
            PreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(preopt_table_name)

            optimization_table_name = f"{concept.lower()}_optimization_summary"
            OptimizationSummaryModel = OPTIMIZATION_SUMMARY_MODEL_MAP.get(optimization_table_name)

            if not OptimizationSummaryModel:
                return Response({
                    "success": False,
                    "message": f"Invalid optimization table for {optimization_table_name}"
                }, status=400)

            qs = OptimizationSummaryModel.objects.filter(scenario_id=scenario_id)
            qs = qs.annotate(
            loc_nm=Subquery(
                PreoptModel.objects.filter(loc_cd=OuterRef('LOC_CD')).values('loc_nm')[:1]
            )
        )
            gdcs_data = qs.values(
                'LOC_CD',
                'loc_nm',
                'GRP_NM',
                'DPT_NM',
                'CLSS_NM',
                'SUB_CLSS_NM',
            ).distinct().order_by('LOC_CD', 'GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM')


            filters = {
                "locations": list(qs.values('LOC_CD', 'loc_nm').distinct()),
                "groups": list(qs.values('GRP_NM').distinct()),
                "departments": list(qs.values('DPT_NM').distinct()),
                "classes": list(qs.values('CLSS_NM').distinct()),
                "sub_classes": list(qs.values('SUB_CLSS_NM').distinct()),
                "gdcs_data": list(gdcs_data),
            }

            return Response({
                "success": True,
                "filters": filters
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            })

class getOptimizationSummary(APIView):
    def post(self, request):
        try:
            data = json.loads(request.body)
            scenario_id = data.get('scenario_id')
            concept = data.get('concept')
            territory = data.get('territory')
            optimization_table_name = f"{concept.lower()}_optimization_summary"
            OptimizationSummaryModel = OPTIMIZATION_SUMMARY_MODEL_MAP.get(optimization_table_name)
            preopt_table_name = f"de_{concept.lower()}_{territory.lower()}_preopt"
            PreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(preopt_table_name)
            # Pagination values
            page = int(data.get('page', 1))
            limit = int(data.get('limit', 10))
            offset = (page - 1) * limit

            # Base queryset (always filter by scenario_id)
            qs = OptimizationSummaryModel.objects.filter(scenario_id=scenario_id)
            qs = qs.annotate(
                loc_nm=Subquery(
                    PreoptModel.objects.filter(loc_cd=OuterRef('LOC_CD')).values('loc_nm')[:1]
                )
            )
            
            location_qs = qs.values('LOC_CD', 'loc_nm').distinct()
            # Apply filters only if values are provided
            if data.get("group"):
                qs = qs.filter(GRP_NM__in=data["group"])

            if data.get("department"):
                qs = qs.filter(DPT_NM__in=data["department"])

            if data.get("class_field"):
                qs = qs.filter(CLSS_NM__in=data["class_field"])

            if data.get("sub_class"):
                qs = qs.filter(SUB_CLSS_NM__in=data["sub_class"])
            
            if data.get("recommendation"):
                qs = qs.filter(Final_Action__in=data["recommendation"])
            
            if data.get("loc_cd"):
                qs = qs.filter(LOC_CD__in=data["loc_cd"])
            
            qs = qs.order_by('GRP_NM', 'DPT_NM', 'CLSS_NM', 'SUB_CLSS_NM', 'LOC_CD')
            
            # Final queryset slice (pagination)
            paginated_qs = qs[offset:offset + limit]

            optimization_data = paginated_qs.values()

            return Response({
                "success": True,
                "data": list(optimization_data),
                "count": qs.count(),
                "locations": list(location_qs) 
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            })

class DownloadOptimizationSummary(APIView):
    def post(self, request):
        try:

            data = json.loads(request.body)
            scenario_id = data.get('scenario_id')
            concept = data.get('concept')
            territory = data.get('territory')
            metric = data.get('metricShortNm')

            if not scenario_id or not concept or not territory:
                return Response({'success': False, 'message': 'Missing required parameters'}, status=400)

            optimization_table_name = f"{concept.lower()}_optimization_summary"
            OptimizationSummaryModel = OPTIMIZATION_SUMMARY_MODEL_MAP.get(optimization_table_name)
            preopt_table_name = f"de_{concept.lower()}_{territory.lower()}_preopt"
            PreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(preopt_table_name)
            
            metric_map = {
                'gmv': 'GMV_sum_reference_month',
                'rev': 'NET_SLS_AMT_sum_reference_month',
            }
            print("metric", metric)
            print("metric2", metric_map.get(metric))
            if metric not in metric_map:
                return Response({'error': 'Invalid metric'}, status=400)

            field_name = metric_map[metric]
            
            qs = OptimizationSummaryModel.objects.filter(scenario_id=scenario_id)
            qs = qs.annotate(
                loc_nm=Subquery(
                    PreoptModel.objects.filter(loc_cd=OuterRef('LOC_CD')).values('loc_nm')[:1]
                )
            )

            results = []
            for obj in qs:
                val = getattr(obj, field_name, None)
                new_val = getattr(obj, 'new_metric', None)

                change_percent = None
                if val not in (None, 0):
                    change_percent = round(((new_val - val) / val) * 100, 2)

                row = {field.name: getattr(obj, field.name) for field in obj._meta.fields}
                row['loc_nm'] = getattr(obj, 'loc_nm', None)
                row[f'{metric}_change_percent'] = change_percent

                results.append(row)

            return Response({
                "success": True,
                "data": results,
                "count": qs.count(),
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            })

            
class GetSaturationCurve(APIView):
    def post(self, request):
        try:
            data = request.data
            concept = data.get("concept")
            scenario_id = data.get("scenario_id")
            cluster = data.get("cluster")
            sub_class = data.get("sub_class")

            # Resolve the model from map
            model_key = f"ds_{concept.lower()}_sat_plot"
            SatPlotModel = SAT_PLOT_MODEL_MAP.get(model_key)

            if not SatPlotModel:
                return Response({
                    "success": False,
                    "message": f"Invalid concept: {concept}"
                }, status=400)

            # Filter rows
            plot_row = SatPlotModel.objects.filter(
                scenario_id=scenario_id,
                CLUSTER_NUM=str(cluster),
                SUB_CLSS_NM=sub_class
            ).values().first()

            if not plot_row:
                return Response({
                    "success": False,
                    "message": "No data found for given filters"
                }, status=404)

            # Parse JSON fields
            try:
                x_raw = json.loads(plot_row["x_raw"]) if plot_row["x_raw"] else []
                y_actual = json.loads(plot_row["y"]) if plot_row["y"] else []
                x_pred_lm = json.loads(plot_row["x_pred_lm"]) if plot_row["x_pred_lm"] else []
                y_pred = json.loads(plot_row["y_pred"]) if plot_row["y_pred"] else []
                PolynomialDegree = plot_row.get("PolynomialDegree", None)
            except Exception as e:
                return Response({
                    "success": False,
                    "message": f"Error parsing JSON fields: {str(e)}"
                }, status=500)

            # Prepare response
            response_data = {
                "x_raw": x_raw,
                "y_actual": y_actual,
                "x_pred_lm": x_pred_lm,
                "y_pred": y_pred,
                "PolynomialDegree": PolynomialDegree,
                "sat_lm": plot_row.get("sat_lm"),
                "perf_at_sat": plot_row.get("perf_at_sat"),
                "subclass_name": plot_row.get("SUB_CLSS_NM"),
                "cluster": plot_row.get("CLUSTER_NUM"),
                "scenario_id": plot_row.get("scenario_id")
            }

            return Response({
                "success": True,
                "data": response_data
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=500)
class getClusterSubclassData(APIView):
    def post(self, request):
        try:
            data = request.data
            concept = data.get("concept")
            scenario_id = data.get("scenario_id")

            model_key = f"ds_{concept.lower()}_sat_plot"
            SatPlotModel = SAT_PLOT_MODEL_MAP.get(model_key)

            if not SatPlotModel:
                return Response({
                    "success": False,
                    "message": f"Invalid concept: {concept}"
                }, status=400)

            clustersSubclass = list(
                SatPlotModel.objects.filter(
                    scenario_id=scenario_id,
                    CLUSTER_NUM__isnull=False
                ).values_list('CLUSTER_NUM', 'SUB_CLSS_NM')
                .distinct()
            )

            clustersSubclass = [
                {"cluster": cluster, "subclass": subclass}
                for cluster, subclass in clustersSubclass
            ]

            return Response({
                "success": True,
                "data": list(clustersSubclass),
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            })

class getRefPeriodData(APIView):
    def get(self, request):
        try:
            concept = request.query_params.get("concept")
            territory = request.query_params.get("territory")
            
            if not concept or not territory:
                return Response({
                    "success": False,
                    "message": "Missing required parameters: concept or territory"
                }, status=400)

            de_preopt_table = f"de_{concept.lower()}_{territory.lower()}_preopt"
            DePreoptModel = DE_PREOPTIMIZATION_MODEL_MAP.get(de_preopt_table)
            # Fetch min and max month from DePreoptModel
            month_range = DePreoptModel.objects.aggregate(
                min_month=Min('month'),
                max_month=Max('month')
            )

            return Response({
                "success": True,
                "start_month": month_range['min_month'],
                "end_month": month_range['max_month'],
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=500)

class getUpliftData(APIView):
    def post(self, request):
        try:
            data = request.data
            concept = data.get("concept")
            scenario_id = data.get("scenario_id")

            if not concept or not scenario_id:
                return Response({
                    "success": False,
                    "message": "Missing required parameters: concept or scenario_id"
                }, status=400)

            upliftTable = f"{concept.lower()}_evaluation_metrics"
            upliftModel = UPLIFT_MAP.get(upliftTable)

            # Filter & fetch only required columns
            upliftData = upliftModel.objects.filter(
                scenario_id=scenario_id
            ).values("daily_pct_lift", "loc_cd", "scenario_id", "evaluation_period_new_matric")

            return Response({
                "success": True,
                "data": list(upliftData)
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=500)
class completeOptimization(APIView):
    def post(self, request):
        scenario_id = request.data.get("scenario_id")
        if not scenario_id:
            return Response({"error": "scenario_id is required"}, status=400)

        new_status = request.data.get("status", "COMPLETED")

        scenario_status = ScenarioStatus.objects.get(scenario_id=scenario_id)
        scenario_status.status = new_status
        scenario_status.save()

        return Response(
            {"message": f"Scenario {scenario_id} updated successfully", "status": scenario_status.status},
            status=200
        )
class CreateOptimizerView(APIView):
    def post(self, request):
        try:
            optimizer = ScenarioMetad.objects.create(
                name="",                # or request.data.get("name", "")
                user_id=None,
                season_type="",
                eval_type=None,
                event_name="",
                eval_start=None,
                eval_end=None,
                ref_start=None,
                ref_end=None,
                CNCPT_NM="",
                TERRITORY_NM="",
                metric="",
                sqft_file=None,
                mdq_file=None,
                cover_file=None,
                exclusion_file_id=None,
                loc_cd="",
                created_by=None,
                updated_by=None,
                created_at=None,
                updated_at=None,
                weights=None,
                sqft_filename="",
                mdq_filename="",
                cover_filename="",
                exclusion_filename="",
                current_page=0,
                progress_page=0,
                run_optimizer=0,
                run_performance=0
            )
            ScenarioStatus.objects.create(scenario=optimizer)  # status defaults to 'CREATED'

            return Response({"success": True, "id": optimizer.id})
        except Exception as e:
            return Response({"success": False, "message": str(e)}, status=500)
class UpdateScenarioStatusView(APIView):
    def post(self, request):
        scenario_id = request.data.get("scenario_id")
        current_page = request.data.get("current_page")
        progress_page = request.data.get("progress_page")

        if not scenario_id:
            return Response({"error": "scenario_id is required"}, status=400)

        try:
            scenario = ScenarioMetad.objects.get(id=scenario_id)

            # ✅ Always update current_page
            if current_page is not None:
                scenario.current_page = current_page

            # ✅ Update progress_page only if incoming value is greater
            if progress_page is not None:
                if scenario.progress_page is None or int(progress_page) > scenario.progress_page:
                    scenario.progress_page = progress_page

            scenario.save()

            return Response({
                "success": True,
                "scenario_id": scenario.id,
                "current_page": scenario.current_page,
                "progress_page": scenario.progress_page
            })

        except ScenarioMetad.DoesNotExist:
            return Response({"error": "Scenario not found"}, status=404)
        except Exception as e:
            return Response({"error": str(e)}, status=500)
class GetTerritoryData(APIView):
    def get(self, request):
        try:
            concept = request.query_params.get("concept")
            if not concept:
                return Response({
                    "success": False,
                    "message": "Missing required parameter: concept"
                }, status=400)

            territories = Territory.objects.filter(
                concept=concept
            ).values("trty_short_name", "trty_display_name").distinct()

            return Response({
                "success": True,
                "territories": list(territories)  # [{'trty_short_name': 'AE', 'trty_display_name': 'UAE'}, ...]
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=500)
class UpdateRunStatusView(APIView):
    def post(self, request):
        try:
            scenario_id = request.data.get("scenario_id")
            run_optimizer = request.data.get("run_optimizer")
            run_performance = request.data.get("run_performance")
            run_range = request.data.get("run_range")

            if not scenario_id:
                return Response({"error": "scenario_id is required"}, status=400)

            scenario = ScenarioMetad.objects.get(id=scenario_id)

            # Update the fields only if they are provided
            if run_optimizer is not None:
                scenario.run_optimizer = bool(run_optimizer)

            if run_performance is not None:
                scenario.run_performance = bool(run_performance)
            
            if run_range is not None:
                scenario.run_range = bool(run_range)

            scenario.save(update_fields=["run_optimizer", "run_performance", "run_range"])

            return Response({
                "success": True,
                "scenario_id": scenario_id,
                "run_optimizer": scenario.run_optimizer,
                "run_performance": scenario.run_performance,
                "run_range": scenario.run_range
            })
        except ScenarioMetad.DoesNotExist:
            return Response({"error": "Scenario not found"}, status=404)
        except Exception as e:
            return Response({"error": str(e)}, status=500)
    
    def get(self, request):
        try:
            scenario_id = request.query_params.get("scenario_id")
            value = request.query_params.get("value")  # runOptimizer or runPerformance

            if not scenario_id:
                return Response({"error": "scenario_id is required"}, status=400)

            # Map camelCase → snake_case field names
            field_map = {
                "runOptimizer": "run_optimizer",
                "runPerformance": "run_performance",
                "runRange": "run_range",
            }

            if value not in field_map:
                return Response({
                    "error": "Invalid value, must be 'runOptimizer' or 'runPerformance'"
                }, status=400)

            field_name = field_map[value]

            scenario = ScenarioMetad.objects.get(id=scenario_id)

            return Response({
                "scenario_id": scenario.id,
                value: getattr(scenario, field_name)  # return in same format as input
            })

        except ScenarioMetad.DoesNotExist:
            return Response({"error": "Scenario not found"}, status=404)
        except Exception as e:
            return Response({"error": str(e)}, status=500)

class StoreDataAvailabilityAPIView(APIView):
    """
    GET /scenario/distinctMonthsPerStore/?concept=hb&territory=ae&store_config=selected_stores&loc_cd=29020&loc_cd=29001
    GET /scenario/distinctMonthsPerStore/?concept=hb&territory=ae
    GET /scenario/distinctMonthsPerStore/?concept=hb&territory=ae&store_config=test_control
    
    - query params:
        - concept: required (e.g., hb)
        - territory: required (e.g., ks)
        - store_config: optional, one of [selected_stores, test_control].
            - selected_stores -> expects loc_cd list; restricts to provided stores
            - test_control -> consider all stores (default)
        - loc_cd: store code when store_config=selected_stores

    - response example item:
        {"store": "29001","months": 14,"min_month": "202408","max_month": "202509"}
    """

    def get(self, request):
        try:
            concept = request.query_params.get("concept")
            territory = request.query_params.get("territory")
            store_config = (request.query_params.get("store_config", "test_control") or "test_control").lower()

            if not concept or not territory:
                return Response({"error": "Missing required parameters: concept and territory"}, status=400)

            de_table = f"de_{concept.lower()}_{territory.lower()}_preopt"

            loc_cds = request.query_params.getlist("loc_cd[]") or request.query_params.getlist("loc_cd")
            if isinstance(loc_cds, str):
                loc_cds = [s.strip() for s in loc_cds.split(",") if s.strip()]

            where_sql = ""
            params = []

            if store_config == "selected_stores":
                if not loc_cds:
                    return Response({"error": "loc_cd list is required when store_config=selected_stores"}, status=400)
                placeholders = ",".join(["%s"] * len(loc_cds))
                where_sql = f" WHERE loc_cd IN ({placeholders})"
                params.extend(loc_cds)
            elif store_config == "test_control":
                pass
            else:
                return Response({"error": "Invalid store_config. Use 'selected_stores' or 'test_control'"}, status=400)

            sql = f"""
                SELECT
                    loc_cd AS store,
                    COUNT(DISTINCT month) AS months,
                    MIN(month) AS min_month,
                    MAX(month) AS max_month
                FROM {de_table}
                {where_sql}
                GROUP BY loc_cd;
            """

            with connection.cursor() as cursor:
                cursor.execute(sql, params)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()

            data = [dict(zip(columns, row)) for row in rows]
            return Response(data, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": "Internal server error", "details": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DownloadPerformanceData(APIView):
    def post(self, request):
        try:
            data = request.data
            concept = data.get("concept")
            territory = data.get("territory")
            scenario_id = data.get("scenario_id")

            if not concept or not territory or not scenario_id:
                return Response({
                    "success": False,
                    "message": "Missing required parameters: concept, territory, or scenario_id"
                }, status=400)

            query = f"""
                    SELECT
                        perf.GRP_NM,
                        perf.DPT_NM,
                        perf.CLSS_NM,
                        perf.SUB_CLSS_NM,
                        perf.d_units,
                        perf.LOC_CD,
                        clus.loc_nm,
                        perf.UNITS_PER_INV,
                        perf.CUST_PEN,
                        perf.COVER,
                        perf.MARGIN_PERC,
                        perf.ASP,
                        perf.lm_bucket_p,
                        perf.cover_bucket_p,
                        perf.action_p,
                        perf.perf_bucket_p,
                        perf.Linear_meter,
                        perf.new_action,
                        perf.Productivity,
                        ROUND(perf.Performance, 2) AS Performance
                    FROM ds_{concept}_performance_ref perf
                    LEFT JOIN de_{concept}_{territory}_cluster clus
                        ON perf.LOC_CD = clus.loc_cd
                    WHERE scenario_id = {scenario_id}
                """
            

            with connection.cursor() as cursor:
                cursor.execute(query)
                columns = [col[0] for col in cursor.description]
                rows = cursor.fetchall()
                data = []
                for row in rows:
                    row_dict = dict(zip(columns, row))
                    row_dict['final_bucket_p'] = (
                        str(row_dict.get('perf_bucket_p', '')) +
                        str(row_dict.get('lm_bucket_p', '')) +
                        str(row_dict.get('cover_bucket_p', ''))
                    )
                    data.append(row_dict)

            return Response({
                "success": True,
                "data": data
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=500)

class getRangeSummary(APIView):
    def get(self, request):
        try:
            
            scenario_id = request.query_params.get('scenario_id')
            concept = request.query_params.get('concept')
            territory = request.query_params.get('territory')
            group = request.query_params.getlist('group')
            department = request.query_params.getlist('department[]')
            class_field = request.query_params.getlist('class_field[]')
            sub_class = request.query_params.getlist('sub_class[]')
            recommendation = request.query_params.getlist('recommendation[]')
            loc_cd = request.query_params.getlist('loc_cd[]')

            if not scenario_id or not concept or not territory:
                return Response({
                    "success": False,
                    "message": "Missing required query parameters: scenario_id, concept, territory"
                })
            
            # Pagination values
            page = int(request.query_params.get('page', 1))
            limit = int(request.query_params.get('limit', 10))
            offset = (page - 1) * limit
            
            prerange_table_name = f"{concept.lower()}_{territory.lower()}_pre_range_optimizer"
            PreRangeModel = DS_PRERANGEOPTIMIZATION_MODEL_MAP.get(prerange_table_name)
           
            # Base queryset (always filter by scenario_id)
            try:
                qs = PreRangeModel.objects.filter(scenario_id=scenario_id)
            except Exception as e:
                print("error is e", e)


            location_qs = qs.values('loc_cd').distinct()
            # Apply filters only if values are provided
            if group:
                qs = qs.filter(grp_nm__in=group)

            if department:
                qs = qs.filter(dpt_nm__in=department)

            if class_field:
                qs = qs.filter(clss_nm__in=class_field)

            if sub_class:
                qs = qs.filter(sub_clss_nm__in=sub_class)
            
            if recommendation:
                qs = qs.filter(final_action__in=recommendation)
            
            if loc_cd:
                qs = qs.filter(loc_cd__in=loc_cd)
            
            qs = qs.order_by('grp_nm', 'dpt_nm', 'clss_nm', 'sub_clss_nm')

            # Final queryset slice (pagination)
            
            paginated_qs = qs[offset:offset + limit]

            optimization_data = paginated_qs.values()

            return Response({
                "success": True,
                "data": list(optimization_data),
                "count": qs.count(),
                "locations": list(location_qs) 
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            })


class DownloadRangeDataCSV(APIView):
    def get(self, request, *args, **kwargs):

        scenario_id = request.query_params.get('scenario_id')
        concept = request.query_params.get('concept')
        territory = request.query_params.get('territory')
        # List of columns to export
        headers = [
            "STND_TRRTRY_NM", "LOC_CD", "GRP_NM", "DPT_NM", "CLSS_NM", "SUB_CLSS_NM", "ITM_CD",
            "ST_SOH", "SOH_IN_TRTRY", "WH_SOH",
            "ST_GMV", "STR_WK_TRADED", "ST_RTL_QTY", "ST_ROS", "COVER",
            "TRTRY_GMV", "TRTRY_RTL_QTY", "TRTRY_WK_TRADED", "NUM_STORES_TRADED", "TRTRY_ROS", "GMV_PER_WK_PER_STORE",
            "RANKING", "optimized_no_of_options", "DEPTH", "optimized_qty",
            "Approach", "Transfer_Quantity", "Cumulative_Transfer_Quantity", "Sorting", "Range", "Final_Quantity", "RANGE_BASED_MAX_LM"
        ]

        rangeBasedRankingTable = f"ds_{concept}_{territory}_range_based_ranking"

        # Build SQL
        escaped_headers = [f"`{col}`" if col != "ITM_CD" else "CONCAT('', `ITM_CD`) AS ITM_CD" for col in headers]
        sql = f'SELECT {", ".join(escaped_headers)} FROM {rangeBasedRankingTable} WHERE scenario_id = %s'

        with connection.cursor() as cursor:
            cursor.execute(sql, [scenario_id])
            rows = cursor.fetchall()
        

        # Prepare CSV response
        filename = f"Range_{concept}_{territory}.csv"
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)
        writer.writerow(headers)  # write header
        for row in rows:
            row = list(row)  # Convert tuple to list (if needed)
    
            itm_cd_index = headers.index("ITM_CD")
            
            # Wrap the ITM_CD value in ="..." to keep Excel from using scientific notation
            if row[itm_cd_index] is not None:
                row[itm_cd_index] = f'="{row[itm_cd_index]}"'
            writer.writerow(row)
        
        return response


class DownloadRangeSummaryCSV(APIView):
    def get(self, request):
        scenario_id = request.query_params.get('scenario_id')
        concept = request.query_params.get('concept')
        territory = request.query_params.get('territory')

        if not scenario_id or not concept or not territory:
            return Response({
                "success": False,
                "message": "Missing required parameters: scenario_id, concept, territory"
            })

        # Dynamically get model
        table_name = f"{concept.lower()}_{territory.lower()}_pre_range_optimizer"
        PreRangeModel = DS_PRERANGEOPTIMIZATION_MODEL_MAP.get(table_name)

        if not PreRangeModel:
            return Response({"success": False, "message": "Model not found"})

        qs = PreRangeModel.objects.filter(scenario_id=scenario_id)


        values = list(qs.values())
        if not values:
            return Response({"success": False, "message": "No data found for filters."})

        # Dynamically get column headers
        export_columns = [
            ('loc_cd', 'Store'),
            ('grp_nm', 'Group'),
            ('dpt_nm', 'Department'),
            ('clss_nm', 'Class'),
            ('sub_clss_nm', 'Subclass'),
            ('min_lm', 'Min LM'),
            ('max_sat_lm', 'Max LM'),
            ('current_lm', 'Current LM'),
            ('optimized_lm', 'Optimized LM'),
            ('lm_delta', 'Space Change Absolute'),
            # ('space_change_precent', 'Space Change %'),
            ('gmv_sum_reference_month', 'GMV'),  # or net_sls_amt... based on metric
            ('new_metric', 'Optimized GMV'),
            # ('gmv_change_percent', 'GMV Change %'),
            ('final_action', 'Recommendation'),
            ('optimized_no_of_options', 'Optimized Options'),
            ('depth', 'Depth'),
            ('optimized_qty', 'Optimized Quantity'),
            ('range_based_max_lm', 'Range Max LM')
        ]

        # Prepare CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="RangeSummary_{concept}_{territory}.csv"'

        writer = csv.writer(response)
        writer.writerow([header for _, header in export_columns])
        
        for row in values:
            writer.writerow([row.get(field, '') for field, _ in export_columns])

        return response

class DeleteFileAPIView(APIView):

    def delete(self, request):
        scenario_id = request.data.get("scenario_id")
        file_type = request.data.get("file_type")

        if not scenario_id or not file_type:
            return Response(
                {"error": "scenario_id, and file_type are required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        file_type = file_type.lower()
        if file_type not in FILE_MODELS:
            return Response(
                {"error": f"Invalid file_type. Must be one of: {', '.join(FILE_MODELS.keys())}"},
                status=status.HTTP_400_BAD_REQUEST
            )

        Model = FILE_MODELS[file_type]

        try:
            with transaction.atomic():
                scenario = ScenarioMetad.objects.select_for_update().filter(id=scenario_id).first()
                
                if not scenario:
                    return Response({"error": "Scenario not found"}, status=status.HTTP_404_NOT_FOUND)
                
                setattr(scenario, f"{file_type}_file_id", None) 
                setattr(scenario, f"{file_type}_filename", None) 
                scenario.save(update_fields=[f"{file_type}_file_id",f"{file_type}_filename"])  

                
                deleted_count, _ = Model.objects.filter(scenario_id=scenario_id).delete()

            return Response(
                {"success": True, "deleted_rows": deleted_count},
                status=status.HTTP_200_OK
            )

        except Exception as e:
            return Response({"error": f"Error deleting file: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class getRangeUpliftData(APIView):
    def get(self, request):
        try:
            
            concept = request.query_params.get("concept")
            scenario_id = request.query_params.get("scenario_id")

            if not concept or not scenario_id:
                return Response({
                    "success": False,
                    "message": "Missing required parameters: concept or scenario_id"
                }, status=400)

            upliftTable = f"{concept.lower()}_range_evaluation_metrics"
            upliftModel = RANGE_UPLIFT_MAP.get(upliftTable)

            # Filter & fetch only required columns
            upliftData = upliftModel.objects.filter(
                scenario_id=scenario_id
            ).values("daily_pct_lift", "loc_cd", "scenario_id", "evaluation_period_new_matric")

            return Response({
                "success": True,
                "data": list(upliftData)
            })

        except Exception as e:
            return Response({
                "success": False,
                "message": str(e)
            }, status=500)

