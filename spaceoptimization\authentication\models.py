from django.db import models
from django.utils import timezone

class UserConceptMapping(models.Model):
    id = models.BigAutoField(primary_key=True)  # Auto increment BIGINT
    user_id = models.IntegerField()             # USER_ID as int
    concept_nm = models.Char<PERSON><PERSON>(max_length=255)  # CONCEPT_NM as varchar(255)

    class Meta:
        db_table = "user_concept_mapping"
        unique_together = ('user_id', 'concept_nm')

from django.db import models


class Role(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.Char<PERSON>ield(max_length=32, null=True, blank=True)

    class Meta:
        db_table = "role"


class Users(models.Model):
    id = models.AutoField(primary_key=True)
    first_name = models.Char<PERSON>ield(max_length=64, null=True, blank=True)
    last_name = models.Char<PERSON>ield(max_length=64, null=True, blank=True)
    email = models.CharField(max_length=64, null=True, blank=True)
    role = models.ForeignKey(
        Role, on_delete=models.SET_NULL, null=True, blank=True, db_column="role_id"
    )
    is_active = models.BooleanField(default=True, null=True, blank=True)
    created_at = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        db_table = "users"

    # Add authentication properties required by Django
    @property
    def is_authenticated(self):
        """Always return True for authenticated users"""
        return True

    @property
    def is_anonymous(self):
        """Always return False for authenticated users"""
        return False

    def __str__(self):
        return f"{self.email} ({self.first_name} {self.last_name})"

class UserActivityLog(models.Model):
    user = models.ForeignKey(Users, on_delete=models.CASCADE, related_name="login_logs")
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True, blank=True)
    login_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = "user_activity_log"
        ordering = ["-login_at"]

    def __str__(self):
        return f"{self.user.email} logged in at {self.login_at}"

class ApiRequestLog(models.Model):
    endpoint = models.CharField(max_length=255)
    user = models.ForeignKey(Users, on_delete=models.SET_NULL, null=True, blank=True)
    status_code = models.IntegerField()
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = "api_request_log"
        ordering = ["-timestamp"]

    def __str__(self):
        return f"{self.endpoint} requested by {self.user.email} - {self.status_code}"